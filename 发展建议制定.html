<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 发展建议制定 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .cluster-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-cluster-1 { background-color: rgba(49, 150, 154, 0.1); color: #31969A; }
      .badge-cluster-2 { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-cluster-3 { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-cluster-4 { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .badge-cluster-5 { background-color: rgba(129, 153, 199, 0.1); color: #8199C7; }
      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-draft { background-color: rgba(148, 163, 184, 0.1); color: #94A3B8; }
      .badge-review { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-approved { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-implemented { background-color: rgba(49, 150, 154, 0.1); color: #31969A; }
      .priority-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-high { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .badge-medium { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-low { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-lightbulb-o text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 发展建议制定</h1>
          <p class="text-sm text-text-secondary">基于聚类画像分析结果，制定针对性的村庄发展建议</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">
    <!-- 发展建议制定面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">发展建议制定</h2>
      
      <!-- 基础信息配置 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">基础信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">建议编号 *</label>
            <input type="text" id="suggestion-id" placeholder="系统自动生成" value="SUG-2025-001" readonly class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">目标对象 *</label>
            <div class="relative">
              <select id="target-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="">请选择目标对象</option>
                <option value="single">单个村庄</option>
                <option value="cluster">聚类类别</option>
                <option value="region">区域范围</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">具体目标 *</label>
            <div class="relative">
              <select id="target-specific" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="">请先选择目标对象</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 问题诊断 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">主要问题诊断</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">问题类别</label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="industry">
                <span class="text-sm">产业发展问题</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="infrastructure">
                <span class="text-sm">基础设施问题</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="population">
                <span class="text-sm">人口流失问题</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="environment">
                <span class="text-sm">环境保护问题</span>
              </label>
            </div>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">优先级</label>
            <div class="relative">
              <select id="priority-level" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="">请选择优先级</option>
                <option value="high">高优先级</option>
                <option value="medium">中优先级</option>
                <option value="low">低优先级</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">问题详细描述 *</label>
          <textarea id="problem-description" rows="4" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请详细描述发现的主要问题，包括具体表现、影响程度和原因分析">基于聚类分析结果，该村庄在产业发展方面存在结构单一、技术含量低的问题，主要表现为传统农业占比过高，缺乏现代化产业支撑，导致村民收入增长缓慢，青壮年人口外流严重。</textarea>
        </div>
      </div>

      <!-- 发展建议内容 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">发展建议内容</h3>
        <div class="space-y-4">
          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">短期建议（1年内）*</label>
            <textarea id="short-term-suggestions" rows="3" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请输入短期内可实施的具体建议措施">1. 完善基础设施建设，改善交通和网络条件
2. 开展农业技术培训，提升农产品质量
3. 建立农产品销售合作社，拓宽销售渠道</textarea>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">中期建议（1-3年）*</label>
            <textarea id="medium-term-suggestions" rows="3" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请输入中期发展的重点建议措施">1. 发展农产品深加工产业，延长产业链
2. 引进现代农业技术，建设智慧农业示范基地
3. 发展乡村旅游，打造特色文化品牌</textarea>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">长期建议（3-5年）*</label>
            <textarea id="long-term-suggestions" rows="3" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请输入长期发展的战略性建议">1. 构建完整的产业生态系统，形成产业集群
2. 建设现代化的物流配送体系
3. 打造区域性农业产业化龙头企业</textarea>
          </div>
        </div>
      </div>

      <!-- 建议依据 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">建议依据</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">数据依据</label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="cluster-analysis" checked>
                <span class="text-sm">聚类分析结果</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="power-data" checked>
                <span class="text-sm">电力数据分析</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="economic-data">
                <span class="text-sm">经济统计数据</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="field-research">
                <span class="text-sm">实地调研结果</span>
              </label>
            </div>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">政策依据</label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="rural-revitalization" checked>
                <span class="text-sm">乡村振兴战略</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="poverty-prevention">
                <span class="text-sm">防返贫监测政策</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="industrial-policy">
                <span class="text-sm">产业发展政策</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="rounded border-border mr-2" value="environmental-policy">
                <span class="text-sm">生态环保政策</span>
              </label>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">详细依据说明</label>
          <textarea id="basis-description" rows="3" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请详细说明制定建议的具体依据和理由">根据聚类分析结果，该村庄属于"待提升村庄"类别，综合指数得分65.8分，产业用电占比仅38.2%，远低于发达型村庄的68.5%。结合2025年中央一号文件关于"持续巩固拓展脱贫攻坚成果"的要求，以及重庆市乡村振兴发展规划，制定上述针对性发展建议。</textarea>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-load-template" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-file-text-o mr-1"></i> 加载模板
          </button>
          <button id="btn-ai-assist" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-magic mr-1"></i> AI辅助生成
          </button>
          <button id="btn-preview" class="px-4 py-3 bg-warning text-white rounded-lg border border-warning hover:bg-warning/90 transition-colors">
            <i class="fa fa-eye mr-1"></i> 预览报告
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-save-draft" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-save mr-1"></i> 保存草稿
          </button>
          <button id="btn-submit" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
            <i class="fa fa-check mr-1"></i> 提交审核
          </button>
        </div>
      </div>
    </div>

    <!-- 建议查询面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">建议查询管理</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">目标村名称</label>
          <input type="text" id="search-village-name" placeholder="请输入村庄名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">聚类类别</label>
          <div class="relative">
            <select id="search-cluster-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类别</option>
              <option value="发达型村庄">发达型村庄</option>
              <option value="成长型村庄">成长型村庄</option>
              <option value="平衡型村庄">平衡型村庄</option>
              <option value="待提升村庄">待提升村庄</option>
              <option value="特色型村庄">特色型村庄</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">建议状态</label>
          <div class="relative">
            <select id="search-status" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部状态</option>
              <option value="草稿">草稿</option>
              <option value="待审核">待审核</option>
              <option value="已通过">已通过</option>
              <option value="已实施">已实施</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">创建人</label>
          <input type="text" id="search-creator" placeholder="请输入创建人" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search-suggestions" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset-search" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-batch-export" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-download mr-1"></i> 批量导出
          </button>
          <button id="btn-new-suggestion" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
            <i class="fa fa-plus mr-1"></i> 新建建议
          </button>
        </div>
      </div>
    </div>

    <!-- 建议列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">发展建议列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-filter mr-1"></i> 高级筛选
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 5%;">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">建议编号</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 15%;">目标对象</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 20%;">主要问题诊断</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 15%;">发展建议摘要</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">优先级</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">建议状态</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">创建人</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 9%;">更新时间</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 10%;">操作</th>
            </tr>
          </thead>
          <tbody id="suggestions-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">SUG-2025-001</div>
                <div class="text-sm text-text-secondary mt-1">系统生成</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">梅花村</div>
                <div class="text-sm text-text-secondary mt-1">
                  <span class="cluster-badge badge-cluster-4">待提升村庄</span>
                </div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">产业结构单一，技术含量低，缺乏现代化产业支撑，青壮年人口外流严重</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">完善基础设施，发展农产品深加工，引进现代农业技术，发展乡村旅游</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="priority-badge badge-high">高优先级</span>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="status-badge badge-approved">已通过</span>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">张专员</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-08-04</div>
                <div class="text-xs text-text-secondary">10:30</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-success hover:text-success/80 transition-colors mr-2" title="导出报告">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">SUG-2025-002</div>
                <div class="text-sm text-text-secondary mt-1">系统生成</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">成长型村庄</div>
                <div class="text-sm text-text-secondary mt-1">
                  <span class="cluster-badge badge-cluster-2">聚类类别</span>
                </div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">基础设施相对落后，产业转型升级缓慢，人才流失问题突出</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">加快产业结构调整，完善基础设施建设，加强人才培养和引进</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="priority-badge badge-medium">中优先级</span>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="status-badge badge-review">待审核</span>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">李分析师</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-08-03</div>
                <div class="text-xs text-text-secondary">16:45</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-success hover:text-success/80 transition-colors mr-2" title="导出报告">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">SUG-2025-003</div>
                <div class="text-sm text-text-secondary mt-1">系统生成</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">竹林村</div>
                <div class="text-sm text-text-secondary mt-1">
                  <span class="cluster-badge badge-cluster-5">特色型村庄</span>
                </div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">旅游配套设施不完善，品牌知名度不高，产业链条较短</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">深化特色产业发展，完善旅游配套设施，加强品牌建设和推广</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="priority-badge badge-medium">中优先级</span>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="status-badge badge-implemented">已实施</span>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">王顾问</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-08-02</div>
                <div class="text-xs text-text-secondary">14:20</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-success hover:text-success/80 transition-colors mr-2" title="导出报告">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">SUG-2025-004</div>
                <div class="text-sm text-text-secondary mt-1">系统生成</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">石桥村</div>
                <div class="text-sm text-text-secondary mt-1">
                  <span class="cluster-badge badge-cluster-3">平衡型村庄</span>
                </div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">发展缺乏特色亮点，产业竞争力不强，创新能力有待提升</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">优化产业结构，加强特色产业培育，完善公共服务体系</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="priority-badge badge-low">低优先级</span>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="status-badge badge-draft">草稿</span>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">陈研究员</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-08-01</div>
                <div class="text-xs text-text-secondary">09:15</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-success hover:text-success/80 transition-colors mr-2" title="导出报告">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">SUG-2025-005</div>
                <div class="text-sm text-text-secondary mt-1">系统生成</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">龙门村</div>
                <div class="text-sm text-text-secondary mt-1">
                  <span class="cluster-badge badge-cluster-1">发达型村庄</span>
                </div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">需要发挥示范带动作用，提升创新驱动能力，推动高质量发展</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">继续发挥产业优势，加强创新驱动，发挥示范带动作用</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="priority-badge badge-low">低优先级</span>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="status-badge badge-approved">已通过</span>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">刘主任</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-07-31</div>
                <div class="text-xs text-text-secondary">11:30</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-success hover:text-success/80 transition-colors mr-2" title="导出报告">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 5 条，每页显示 5 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 目标对象选择联动
      const targetTypeSelect = document.getElementById('target-type');
      const targetSpecificSelect = document.getElementById('target-specific');

      const targetOptions = {
        'single': [
          { value: '龙门村', text: '龙门村（发达型）' },
          { value: '青山村', text: '青山村（成长型）' },
          { value: '石桥村', text: '石桥村（平衡型）' },
          { value: '梅花村', text: '梅花村（待提升）' },
          { value: '竹林村', text: '竹林村（特色型）' }
        ],
        'cluster': [
          { value: '发达型村庄', text: '发达型村庄（23个村庄）' },
          { value: '成长型村庄', text: '成长型村庄（31个村庄）' },
          { value: '平衡型村庄', text: '平衡型村庄（28个村庄）' },
          { value: '待提升村庄', text: '待提升村庄（18个村庄）' },
          { value: '特色型村庄', text: '特色型村庄（15个村庄）' }
        ],
        'region': [
          { value: '巫山县', text: '巫山县（25个村庄）' },
          { value: '奉节县', text: '奉节县（22个村庄）' },
          { value: '巫溪县', text: '巫溪县（20个村庄）' },
          { value: '城口县', text: '城口县（18个村庄）' },
          { value: '云阳县', text: '云阳县（30个村庄）' }
        ]
      };

      targetTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        targetSpecificSelect.innerHTML = '<option value="">请选择具体目标</option>';

        if (selectedType && targetOptions[selectedType]) {
          targetOptions[selectedType].forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            targetSpecificSelect.appendChild(optionElement);
          });
        }
      });

      // 加载模板按钮
      document.getElementById('btn-load-template').addEventListener('click', function() {
        const templates = [
          '产业发展类建议模板',
          '基础设施建设模板',
          '人才培养类建议模板',
          '生态保护类建议模板',
          '综合发展建议模板'
        ];

        const templateChoice = prompt('请选择建议模板：\n' + templates.map((t, i) => `${i+1}. ${t}`).join('\n'));
        if (templateChoice && templateChoice >= 1 && templateChoice <= 5) {
          alert(`已加载"${templates[templateChoice-1]}"模板`);
          // 这里可以加载对应的模板内容
        }
      });

      // AI辅助生成按钮
      document.getElementById('btn-ai-assist').addEventListener('click', function() {
        const targetType = document.getElementById('target-type').value;
        const targetSpecific = document.getElementById('target-specific').value;

        if (!targetType || !targetSpecific) {
          alert('请先选择目标对象和具体目标');
          return;
        }

        alert(`AI辅助生成\n\n正在基于"${targetSpecific}"的聚类分析结果生成发展建议...\n\n这里将调用AI模型，根据聚类画像特征自动生成针对性的发展建议内容。`);

        // 模拟AI生成的建议内容
        if (targetSpecific === '梅花村') {
          document.getElementById('problem-description').value = '基于聚类分析结果，该村庄在产业发展方面存在结构单一、技术含量低的问题，主要表现为传统农业占比过高，缺乏现代化产业支撑，导致村民收入增长缓慢，青壮年人口外流严重。';
          document.getElementById('short-term-suggestions').value = '1. 完善基础设施建设，改善交通和网络条件\n2. 开展农业技术培训，提升农产品质量\n3. 建立农产品销售合作社，拓宽销售渠道';
          document.getElementById('medium-term-suggestions').value = '1. 发展农产品深加工产业，延长产业链\n2. 引进现代农业技术，建设智慧农业示范基地\n3. 发展乡村旅游，打造特色文化品牌';
          document.getElementById('long-term-suggestions').value = '1. 构建完整的产业生态系统，形成产业集群\n2. 建设现代化的物流配送体系\n3. 打造区域性农业产业化龙头企业';
        }
      });

      // 预览报告按钮
      document.getElementById('btn-preview').addEventListener('click', function() {
        const suggestionId = document.getElementById('suggestion-id').value;
        const targetSpecific = document.getElementById('target-specific').value;

        if (!targetSpecific) {
          alert('请先完善建议内容');
          return;
        }

        alert(`预览发展建议报告\n\n建议编号：${suggestionId}\n目标对象：${targetSpecific}\n\n这里将生成完整的发展建议报告预览，包括问题诊断、建议内容、实施计划等。`);
      });

      // 保存草稿按钮
      document.getElementById('btn-save-draft').addEventListener('click', function() {
        const formData = {
          suggestionId: document.getElementById('suggestion-id').value,
          targetType: document.getElementById('target-type').value,
          targetSpecific: document.getElementById('target-specific').value,
          problemDescription: document.getElementById('problem-description').value,
          shortTermSuggestions: document.getElementById('short-term-suggestions').value,
          mediumTermSuggestions: document.getElementById('medium-term-suggestions').value,
          longTermSuggestions: document.getElementById('long-term-suggestions').value
        };

        console.log('保存草稿:', formData);
        alert('发展建议草稿保存成功！');
      });

      // 提交审核按钮
      document.getElementById('btn-submit').addEventListener('click', function() {
        const requiredFields = [
          { id: 'target-type', name: '目标对象' },
          { id: 'target-specific', name: '具体目标' },
          { id: 'problem-description', name: '问题详细描述' },
          { id: 'short-term-suggestions', name: '短期建议' },
          { id: 'medium-term-suggestions', name: '中期建议' },
          { id: 'long-term-suggestions', name: '长期建议' }
        ];

        const missingFields = requiredFields.filter(field => !document.getElementById(field.id).value);

        if (missingFields.length > 0) {
          alert(`请完善以下必填项：\n${missingFields.map(f => f.name).join('\n')}`);
          return;
        }

        if (confirm('确定要提交审核吗？提交后将无法修改。')) {
          alert('发展建议已提交审核！\n\n审核通过后将可以导出正式报告。');
        }
      });

      // 查询按钮事件
      document.getElementById('btn-search-suggestions').addEventListener('click', function() {
        const searchParams = {
          villageName: document.getElementById('search-village-name').value,
          clusterType: document.getElementById('search-cluster-type').value,
          status: document.getElementById('search-status').value,
          creator: document.getElementById('search-creator').value
        };

        console.log('查询条件:', searchParams);
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置查询按钮
      document.getElementById('btn-reset-search').addEventListener('click', function() {
        document.getElementById('search-village-name').value = '';
        document.getElementById('search-cluster-type').value = '';
        document.getElementById('search-status').value = '';
        document.getElementById('search-creator').value = '';
      });

      // 批量导出按钮
      document.getElementById('btn-batch-export').addEventListener('click', function() {
        const checkedRows = document.querySelectorAll('#suggestions-table-body input[type="checkbox"]:checked');
        if (checkedRows.length === 0) {
          alert('请先选择要导出的建议');
          return;
        }
        alert(`批量导出发展建议报告\n\n已选择 ${checkedRows.length} 条建议，将生成综合报告文档。`);
      });

      // 新建建议按钮
      document.getElementById('btn-new-suggestion').addEventListener('click', function() {
        // 重置表单
        document.getElementById('target-type').value = '';
        document.getElementById('target-specific').innerHTML = '<option value="">请先选择目标对象</option>';
        document.getElementById('problem-description').value = '';
        document.getElementById('short-term-suggestions').value = '';
        document.getElementById('medium-term-suggestions').value = '';
        document.getElementById('long-term-suggestions').value = '';
        document.getElementById('basis-description').value = '';

        // 生成新的建议编号
        const now = new Date();
        const newId = `SUG-${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`;
        document.getElementById('suggestion-id').value = newId;

        alert('已创建新的发展建议\n\n请填写相关信息');
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-eye')) {
          const row = e.target.closest('tr');
          const suggestionId = row.cells[1].querySelector('.font-medium').textContent;
          alert(`查看建议详情：${suggestionId}\n\n这里将显示完整的发展建议内容和实施情况`);
        }

        if (e.target.closest('.fa-edit')) {
          const row = e.target.closest('tr');
          const suggestionId = row.cells[1].querySelector('.font-medium').textContent;
          alert(`编辑发展建议：${suggestionId}\n\n这里将打开编辑界面，允许修改建议内容`);
        }

        if (e.target.closest('.fa-download')) {
          const row = e.target.closest('tr');
          const suggestionId = row.cells[1].querySelector('.font-medium').textContent;
          alert(`导出建议报告：${suggestionId}\n\n正在生成Word格式的发展建议报告...`);
        }

        if (e.target.closest('.fa-trash')) {
          const row = e.target.closest('tr');
          const suggestionId = row.cells[1].querySelector('.font-medium').textContent;
          if (confirm(`确定要删除建议"${suggestionId}"吗？此操作不可恢复。`)) {
            alert('发展建议已删除');
          }
        }
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });

      // 初始化建议编号
      const now = new Date();
      const initialId = `SUG-${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}-001`;
      document.getElementById('suggestion-id').value = initialId;
    });
  </script>
</body>

</html>
