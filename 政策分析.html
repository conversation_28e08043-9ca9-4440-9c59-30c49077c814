<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 政策分析 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-pending { background-color: rgba(148, 163, 184, 0.1); color: #94A3B8; }
      .badge-analyzing { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-completed { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-failed { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .policy-card {
        transition: all 0.3s ease;
        cursor: pointer;
      }
      .policy-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(49, 150, 154, 0.15);
      }
      .policy-card.selected {
        border-color: #31969A;
        background-color: rgba(49, 150, 154, 0.05);
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .progress-bar {
        height: 8px;
        background-color: #E2E8F0;
        border-radius: 4px;
        overflow: hidden;
      }
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #31969A, #80CCE3);
        transition: width 0.3s ease;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-search text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 政策分析</h1>
          <p class="text-sm text-text-secondary">基于大模型的政策文件自动化分析与要素提取</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">
    <!-- 分析统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">政策库总数</p>
            <h3 class="text-3xl font-bold mt-2">1,248 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-file-text text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 12.5%
          </span>
          <span class="text-text-secondary ml-2">较上月增加</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">已分析政策</p>
            <h3 class="text-3xl font-bold mt-2">856 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-check mr-1"></i> 68.6%
          </span>
          <span class="text-text-secondary ml-2">分析覆盖率</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">分析中任务</p>
            <h3 class="text-3xl font-bold mt-2">15 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-spinner text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-warning flex items-center">
            <i class="fa fa-clock-o mr-1"></i> 进行中
          </span>
          <span class="text-text-secondary ml-2">预计5分钟完成</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">平均分析时长</p>
            <h3 class="text-3xl font-bold mt-2">2.3 <span class="text-lg font-normal text-text-secondary">分钟</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-clock-o text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-down mr-1"></i> 18.2%
          </span>
          <span class="text-text-secondary ml-2">效率提升</span>
        </div>
      </div>
    </div>

    <!-- 政策选择与分析面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">政策选择与分析</h2>
      
      <!-- 政策库浏览 -->
      <div class="mb-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-text-primary">政策库浏览</h3>
          <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
              <label class="text-sm text-text-secondary">筛选:</label>
              <select id="policy-filter" class="bg-light border border-border rounded px-3 py-1 text-sm">
                <option value="all">全部政策</option>
                <option value="unanalyzed">未分析</option>
                <option value="analyzed">已分析</option>
                <option value="analyzing">分析中</option>
              </select>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-text-secondary">已选择:</span>
              <span id="selected-count" class="font-semibold text-primary">0</span>
              <span class="text-sm text-text-secondary">个政策</span>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4 max-h-96 overflow-y-auto scrollbar-thin">
          <!-- 政策卡片 -->
          <div class="policy-card bg-light border border-border rounded-lg p-4" data-policy-id="1">
            <div class="flex items-start justify-between mb-3">
              <input type="checkbox" class="policy-checkbox rounded border-border mt-1">
              <span class="status-badge badge-pending">未分析</span>
            </div>
            <h4 class="font-medium text-text-primary mb-2 line-clamp-2">关于推进乡村振兴战略实施的指导意见</h4>
            <div class="text-sm text-text-secondary space-y-1">
              <div>发文单位: 国务院</div>
              <div>发布日期: 2025-01-15</div>
              <div>文件类型: 指导意见</div>
            </div>
          </div>

          <div class="policy-card bg-light border border-border rounded-lg p-4" data-policy-id="2">
            <div class="flex items-start justify-between mb-3">
              <input type="checkbox" class="policy-checkbox rounded border-border mt-1">
              <span class="status-badge badge-completed">已分析</span>
            </div>
            <h4 class="font-medium text-text-primary mb-2 line-clamp-2">电力体制改革深化实施方案</h4>
            <div class="text-sm text-text-secondary space-y-1">
              <div>发文单位: 国家发改委</div>
              <div>发布日期: 2024-12-20</div>
              <div>文件类型: 实施方案</div>
            </div>
          </div>

          <div class="policy-card bg-light border border-border rounded-lg p-4" data-policy-id="3">
            <div class="flex items-start justify-between mb-3">
              <input type="checkbox" class="policy-checkbox rounded border-border mt-1">
              <span class="status-badge badge-analyzing">分析中</span>
            </div>
            <h4 class="font-medium text-text-primary mb-2 line-clamp-2">新能源发展"十四五"规划纲要</h4>
            <div class="text-sm text-text-secondary space-y-1">
              <div>发文单位: 国家能源局</div>
              <div>发布日期: 2024-11-30</div>
              <div>文件类型: 规划纲要</div>
            </div>
          </div>

          <div class="policy-card bg-light border border-border rounded-lg p-4" data-policy-id="4">
            <div class="flex items-start justify-between mb-3">
              <input type="checkbox" class="policy-checkbox rounded border-border mt-1">
              <span class="status-badge badge-pending">未分析</span>
            </div>
            <h4 class="font-medium text-text-primary mb-2 line-clamp-2">农村电网改造升级专项行动计划</h4>
            <div class="text-sm text-text-secondary space-y-1">
              <div>发文单位: 国家电网</div>
              <div>发布日期: 2025-02-01</div>
              <div>文件类型: 行动计划</div>
            </div>
          </div>

          <div class="policy-card bg-light border border-border rounded-lg p-4" data-policy-id="5">
            <div class="flex items-start justify-between mb-3">
              <input type="checkbox" class="policy-checkbox rounded border-border mt-1">
              <span class="status-badge badge-completed">已分析</span>
            </div>
            <h4 class="font-medium text-text-primary mb-2 line-clamp-2">脱贫攻坚成果巩固拓展实施细则</h4>
            <div class="text-sm text-text-secondary space-y-1">
              <div>发文单位: 农业农村部</div>
              <div>发布日期: 2024-10-15</div>
              <div>文件类型: 实施细则</div>
            </div>
          </div>

          <div class="policy-card bg-light border border-border rounded-lg p-4" data-policy-id="6">
            <div class="flex items-start justify-between mb-3">
              <input type="checkbox" class="policy-checkbox rounded border-border mt-1">
              <span class="status-badge badge-pending">未分析</span>
            </div>
            <h4 class="font-medium text-text-primary mb-2 line-clamp-2">碳达峰碳中和目标实现路径规划</h4>
            <div class="text-sm text-text-secondary space-y-1">
              <div>发文单位: 生态环境部</div>
              <div>发布日期: 2025-01-08</div>
              <div>文件类型: 路径规划</div>
            </div>
          </div>
        </div>

        <!-- 批量操作 -->
        <div class="flex justify-between items-center">
          <div class="flex space-x-3">
            <button id="btn-select-all" class="px-4 py-2 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
              <i class="fa fa-check-square mr-1"></i> 全选
            </button>
            <button id="btn-select-unanalyzed" class="px-4 py-2 bg-warning/20 text-warning rounded-lg border border-warning/30 hover:bg-warning/30 transition-colors">
              <i class="fa fa-filter mr-1"></i> 选择未分析
            </button>
          </div>
          <div class="flex space-x-3">
            <button id="btn-batch-analyze" class="px-4 py-2 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors disabled:opacity-50" disabled>
              <i class="fa fa-cogs mr-1"></i> 开始分析
            </button>
            <button id="btn-analysis-queue" class="px-4 py-2 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
              <i class="fa fa-list mr-1"></i> 分析队列
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分析结果查询面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">分析结果查询</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">政策标题</label>
          <input type="text" id="policy-title" placeholder="请输入政策标题关键词" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">发文单位</label>
          <div class="relative">
            <select id="issuing-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部单位</option>
              <option value="国务院">国务院</option>
              <option value="国家发改委">国家发改委</option>
              <option value="国家能源局">国家能源局</option>
              <option value="农业农村部">农业农村部</option>
              <option value="生态环境部">生态环境部</option>
              <option value="国家电网">国家电网</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">政策类型</label>
          <div class="relative">
            <select id="policy-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类型</option>
              <option value="指导意见">指导意见</option>
              <option value="实施方案">实施方案</option>
              <option value="规划纲要">规划纲要</option>
              <option value="行动计划">行动计划</option>
              <option value="实施细则">实施细则</option>
              <option value="路径规划">路径规划</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">分析状态</label>
          <div class="relative">
            <select id="analysis-status" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部状态</option>
              <option value="已完成">已完成</option>
              <option value="分析中">分析中</option>
              <option value="待分析">待分析</option>
              <option value="分析失败">分析失败</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">发布日期范围</label>
          <div class="flex items-center space-x-3">
            <input type="date" id="date-start" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            <span class="text-text-secondary">-</span>
            <input type="date" id="date-end" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">关键词搜索</label>
          <input type="text" id="keywords" placeholder="请输入政策内容关键词" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search-policies" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset-search" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
          <button id="btn-advanced-search" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-sliders mr-1"></i> 高级搜索
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-export-results" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出结果
          </button>
          <button id="btn-batch-reanalyze" class="px-4 py-3 bg-warning text-white rounded-lg border border-warning hover:bg-warning/90 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 批量重新分析
          </button>
        </div>
      </div>
    </div>

    <!-- 政策分析列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">政策分析结果</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-success/20 text-success rounded-lg border border-success/30 hover:bg-success/30 transition-colors">
            <i class="fa fa-check mr-1"></i> 已完成 (856)
          </button>
          <button class="px-4 py-2 bg-warning/20 text-warning rounded-lg border border-warning/30 hover:bg-warning/30 transition-colors">
            <i class="fa fa-spinner mr-1"></i> 分析中 (15)
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 5%;">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 20%;">政策标题</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">发文单位</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">发布日期</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 15%;">政策目标</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 15%;">关键举措</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">涉及主体</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">量化指标</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 6%;">状态</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 8%;">操作</th>
            </tr>
          </thead>
          <tbody id="policies-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">电力体制改革深化实施方案</div>
                <div class="text-sm text-text-secondary mt-1">国家发改委 • 2024-12-20</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">国家发改委</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2024-12-20</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">深化电力市场化改革，完善电力价格形成机制，提升电力系统效率</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">推进电力现货市场建设、完善输配电价机制、加强电力监管</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">电网企业、发电企业、售电公司、电力用户</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">市场化交易电量占比达到60%</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-completed">已完成</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="导出分析">
                    <i class="fa fa-download"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="重新分析">
                    <i class="fa fa-refresh"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">脱贫攻坚成果巩固拓展实施细则</div>
                <div class="text-sm text-text-secondary mt-1">农业农村部 • 2024-10-15</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">农业农村部</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2024-10-15</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">巩固拓展脱贫攻坚成果，防止规模性返贫，推进乡村全面振兴</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">建立防返贫监测机制、发展特色产业、完善基础设施</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">脱贫户、监测户、村集体、帮扶单位</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">脱贫人口收入增长10%以上</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-completed">已完成</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="导出分析">
                    <i class="fa fa-download"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="重新分析">
                    <i class="fa fa-refresh"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">新能源发展"十四五"规划纲要</div>
                <div class="text-sm text-text-secondary mt-1">国家能源局 • 2024-11-30</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">国家能源局</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2024-11-30</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">加快新能源开发利用，构建清洁低碳安全高效的能源体系</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">大力发展风电光伏、推进储能技术、完善电网配套</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">新能源企业、电网公司、地方政府</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">新能源装机容量达到12亿千瓦</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-analyzing">分析中</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-text-tertiary transition-colors" title="分析中" disabled>
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-text-tertiary transition-colors" title="分析中" disabled>
                    <i class="fa fa-download"></i>
                  </button>
                  <button class="text-warning hover:text-warning/80 transition-colors" title="查看进度">
                    <i class="fa fa-clock-o"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">关于推进乡村振兴战略实施的指导意见</div>
                <div class="text-sm text-text-secondary mt-1">国务院 • 2025-01-15</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">国务院</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2025-01-15</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">全面推进乡村振兴，加快农业农村现代化，促进农民增收致富</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">发展现代农业、建设美丽乡村、完善治理体系、增进民生福祉</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">农业合作社、村委会、农户、涉农企业</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">农民人均可支配收入增长8%</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-pending">待分析</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-text-tertiary transition-colors" title="待分析" disabled>
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-text-tertiary transition-colors" title="待分析" disabled>
                    <i class="fa fa-download"></i>
                  </button>
                  <button class="text-primary hover:text-primary/80 transition-colors" title="开始分析">
                    <i class="fa fa-play"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">农村电网改造升级专项行动计划</div>
                <div class="text-sm text-text-secondary mt-1">国家电网 • 2025-02-01</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">国家电网</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2025-02-01</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">提升农村电网供电能力和可靠性，支撑乡村振兴和农业现代化</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">改造配电网络、增加变电容量、提升自动化水平</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">电网公司、地方政府、农村用户</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">农村户均配变容量达到2.5kVA</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-completed">已完成</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="导出分析">
                    <i class="fa fa-download"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="重新分析">
                    <i class="fa fa-refresh"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">碳达峰碳中和目标实现路径规划</div>
                <div class="text-sm text-text-secondary mt-1">生态环境部 • 2025-01-08</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">生态环境部</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2025-01-08</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">制定碳达峰碳中和实施路径，推动绿色低碳发展转型</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">优化能源结构、提升能效水平、发展碳汇产业</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">重点企业、地方政府、金融机构</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2030年碳达峰，2060年碳中和</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-completed">已完成</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="导出分析">
                    <i class="fa fa-download"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="重新分析">
                    <i class="fa fa-refresh"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 6 条，每页显示 6 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 政策选择功能
      const policyCards = document.querySelectorAll('.policy-card');
      const policyCheckboxes = document.querySelectorAll('.policy-checkbox');
      const selectedCountElement = document.getElementById('selected-count');
      const batchAnalyzeBtn = document.getElementById('btn-batch-analyze');

      // 政策卡片点击事件
      policyCards.forEach(card => {
        card.addEventListener('click', function(e) {
          if (e.target.type !== 'checkbox') {
            const checkbox = this.querySelector('.policy-checkbox');
            checkbox.checked = !checkbox.checked;
            updateSelection();
          }
        });
      });

      // 复选框变化事件
      policyCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          updateSelection();
        });
      });

      // 更新选择状态
      function updateSelection() {
        const checkedBoxes = document.querySelectorAll('.policy-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCountElement.textContent = count;
        batchAnalyzeBtn.disabled = count === 0;

        // 更新卡片样式
        policyCards.forEach(card => {
          const checkbox = card.querySelector('.policy-checkbox');
          if (checkbox.checked) {
            card.classList.add('selected');
          } else {
            card.classList.remove('selected');
          }
        });
      }

      // 全选按钮
      document.getElementById('btn-select-all').addEventListener('click', function() {
        const allChecked = document.querySelectorAll('.policy-checkbox:checked').length === policyCheckboxes.length;

        policyCheckboxes.forEach(checkbox => {
          checkbox.checked = !allChecked;
        });
        updateSelection();
      });

      // 选择未分析按钮
      document.getElementById('btn-select-unanalyzed').addEventListener('click', function() {
        policyCheckboxes.forEach(checkbox => {
          const card = checkbox.closest('.policy-card');
          const status = card.querySelector('.status-badge');
          checkbox.checked = status.textContent.trim() === '未分析';
        });
        updateSelection();
      });

      // 政策筛选
      document.getElementById('policy-filter').addEventListener('change', function() {
        const filterValue = this.value;

        policyCards.forEach(card => {
          const status = card.querySelector('.status-badge').textContent.trim();
          let show = true;

          switch(filterValue) {
            case 'unanalyzed':
              show = status === '未分析';
              break;
            case 'analyzed':
              show = status === '已分析';
              break;
            case 'analyzing':
              show = status === '分析中';
              break;
            case 'all':
            default:
              show = true;
              break;
          }

          card.style.display = show ? 'block' : 'none';
        });
      });

      // 开始分析按钮
      document.getElementById('btn-batch-analyze').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.policy-checkbox:checked');
        const selectedPolicies = Array.from(checkedBoxes).map(checkbox => {
          const card = checkbox.closest('.policy-card');
          return card.querySelector('h4').textContent;
        });

        if (selectedPolicies.length === 0) return;

        if (confirm(`确定要分析选中的 ${selectedPolicies.length} 个政策吗？\n\n预计分析时间：${selectedPolicies.length * 2.3} 分钟`)) {
          // 模拟开始分析
          checkedBoxes.forEach(checkbox => {
            const card = checkbox.closest('.policy-card');
            const statusBadge = card.querySelector('.status-badge');
            if (statusBadge.textContent.trim() === '未分析') {
              statusBadge.className = 'status-badge badge-analyzing';
              statusBadge.textContent = '分析中';
            }
            checkbox.checked = false;
          });

          updateSelection();
          alert(`已启动 ${selectedPolicies.length} 个政策的分析任务\n\n可在分析队列中查看进度`);
        }
      });

      // 分析队列按钮
      document.getElementById('btn-analysis-queue').addEventListener('click', function() {
        alert('分析队列\n\n当前队列中有 15 个分析任务\n- 进行中: 3个\n- 等待中: 12个\n\n预计全部完成时间: 28分钟');
      });

      // 查询按钮事件
      document.getElementById('btn-search-policies').addEventListener('click', function() {
        const searchParams = {
          policyTitle: document.getElementById('policy-title').value,
          issuingUnit: document.getElementById('issuing-unit').value,
          policyType: document.getElementById('policy-type').value,
          analysisStatus: document.getElementById('analysis-status').value,
          dateStart: document.getElementById('date-start').value,
          dateEnd: document.getElementById('date-end').value,
          keywords: document.getElementById('keywords').value
        };

        console.log('查询条件:', searchParams);
        alert('政策查询功能已触发，请查看控制台输出');
      });

      // 重置查询按钮
      document.getElementById('btn-reset-search').addEventListener('click', function() {
        document.getElementById('policy-title').value = '';
        document.getElementById('issuing-unit').value = '';
        document.getElementById('policy-type').value = '';
        document.getElementById('analysis-status').value = '';
        document.getElementById('date-start').value = '';
        document.getElementById('date-end').value = '';
        document.getElementById('keywords').value = '';
      });

      // 高级搜索按钮
      document.getElementById('btn-advanced-search').addEventListener('click', function() {
        alert('高级搜索\n\n这里将打开高级搜索面板，支持更复杂的搜索条件组合和语义搜索');
      });

      // 导出结果按钮
      document.getElementById('btn-export-results').addEventListener('click', function() {
        alert('导出分析结果\n\n将生成包含所有政策分析结果的Excel文件');
      });

      // 批量重新分析按钮
      document.getElementById('btn-batch-reanalyze').addEventListener('click', function() {
        alert('批量重新分析\n\n这里将重新分析选中的政策，使用最新的模型版本');
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-eye')) {
          const row = e.target.closest('tr');
          const policyTitle = row.cells[1].querySelector('.font-medium').textContent;
          alert(`查看政策详情：${policyTitle}\n\n这里将显示完整的政策结构化分析详情，包括：\n- 政策背景与目标\n- 关键举措详解\n- 涉及主体分析\n- 量化指标解读\n- 实施路径建议`);
        }

        if (e.target.closest('.fa-download')) {
          const row = e.target.closest('tr');
          const policyTitle = row.cells[1].querySelector('.font-medium').textContent;
          alert(`导出分析报告：${policyTitle}\n\n正在生成Word格式的政策分析报告...`);
        }

        if (e.target.closest('.fa-refresh')) {
          const row = e.target.closest('tr');
          const policyTitle = row.cells[1].querySelector('.font-medium').textContent;
          if (confirm(`确定要重新分析"${policyTitle}"吗？\n\n重新分析将使用最新的模型版本，可能会得到更准确的结果。`)) {
            alert('已启动重新分析任务');
          }
        }

        if (e.target.closest('.fa-clock-o')) {
          const row = e.target.closest('tr');
          const policyTitle = row.cells[1].querySelector('.font-medium').textContent;
          alert(`分析进度：${policyTitle}\n\n当前进度：65%\n预计剩余时间：1.2分钟\n\n正在进行：关键举措提取`);
        }
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });

      // 初始化
      updateSelection();
    });
  </script>
</body>

</html>
