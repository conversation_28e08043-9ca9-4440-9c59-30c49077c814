<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 对口帮扶对象筛选 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .cluster-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-cluster-1 { background-color: rgba(49, 150, 154, 0.1); color: #31969A; }
      .badge-cluster-2 { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-cluster-3 { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-cluster-4 { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .badge-cluster-5 { background-color: rgba(129, 153, 199, 0.1); color: #8199C7; }
      .risk-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-low { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-medium { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-high { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-pending { background-color: rgba(148, 163, 184, 0.1); color: #94A3B8; }
      .badge-candidate { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-selected { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .potential-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-high-potential { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-medium-potential { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-low-potential { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-filter text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 对口帮扶对象筛选</h1>
          <p class="text-sm text-text-secondary">基于综合脱贫成效分析，筛选确定最终的对口帮扶对象</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">
    <!-- 筛选统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">候选村庄总数</p>
            <h3 class="text-3xl font-bold mt-2">115 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-home text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 8.5%
          </span>
          <span class="text-text-secondary ml-2">较上期增加</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">待筛选村庄</p>
            <h3 class="text-3xl font-bold mt-2">68 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-clock-o text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-warning flex items-center">
            <i class="fa fa-minus mr-1"></i> 待处理
          </span>
          <span class="text-text-secondary ml-2">需要评估</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">候选村庄</p>
            <h3 class="text-3xl font-bold mt-2">32 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-star-o text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 12.3%
          </span>
          <span class="text-text-secondary ml-2">较上期增加</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">已选定村庄</p>
            <h3 class="text-3xl font-bold mt-2">15 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-check mr-1"></i> 已确定
          </span>
          <span class="text-text-secondary ml-2">帮扶对象</span>
        </div>
      </div>
    </div>

    <!-- 筛选条件面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">筛选条件设置</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">村庄名称</label>
          <input type="text" id="village-name" placeholder="请输入村庄名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属地市</label>
          <div class="relative">
            <select id="city-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部地市</option>
              <option value="重庆市">重庆市</option>
              <option value="万州区">万州区</option>
              <option value="涪陵区">涪陵区</option>
              <option value="渝中区">渝中区</option>
              <option value="大渡口区">大渡口区</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属区县</label>
          <div class="relative">
            <select id="district-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部区县</option>
              <option value="巫山县">巫山县</option>
              <option value="奉节县">奉节县</option>
              <option value="巫溪县">巫溪县</option>
              <option value="城口县">城口县</option>
              <option value="云阳县">云阳县</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">返贫风险等级</label>
          <div class="relative">
            <select id="risk-level" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部等级</option>
              <option value="高风险">高风险</option>
              <option value="中风险">中风险</option>
              <option value="低风险">低风险</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">聚类类别</label>
          <div class="relative">
            <select id="cluster-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类别</option>
              <option value="发达型村庄">发达型村庄</option>
              <option value="成长型村庄">成长型村庄</option>
              <option value="平衡型村庄">平衡型村庄</option>
              <option value="待提升村庄">待提升村庄</option>
              <option value="特色型村庄">特色型村庄</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">综合指数得分区间</label>
          <div class="flex items-center space-x-3">
            <input type="number" id="score-min" placeholder="最低分" min="0" max="100" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            <span class="text-text-secondary">-</span>
            <input type="number" id="score-max" placeholder="最高分" min="0" max="100" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">当前状态</label>
          <div class="relative">
            <select id="current-status" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部状态</option>
              <option value="待筛选">待筛选</option>
              <option value="候选">候选</option>
              <option value="已选定">已选定</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
          <button id="btn-advanced-filter" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-sliders mr-1"></i> 高级筛选
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-export-list" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出列表
          </button>
          <button id="btn-batch-operation" class="px-4 py-3 bg-warning text-white rounded-lg border border-warning hover:bg-warning/90 transition-colors">
            <i class="fa fa-cogs mr-1"></i> 批量操作
          </button>
        </div>
      </div>
    </div>

    <!-- 候选村庄列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">候选村庄列表</h3>
        <div class="flex space-x-3">
          <div class="flex items-center space-x-2">
            <span class="text-sm text-text-secondary">显示:</span>
            <select id="status-filter" class="bg-light border border-border rounded px-3 py-1 text-sm">
              <option value="all">全部状态</option>
              <option value="pending">待筛选</option>
              <option value="candidate">候选</option>
              <option value="selected">已选定</option>
            </select>
          </div>
          <button id="btn-select-all" class="px-4 py-2 bg-success/20 text-success rounded-lg border border-success/30 hover:bg-success/30 transition-colors">
            <i class="fa fa-check-square mr-1"></i> 全选
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 5%;">
                <input type="checkbox" id="select-all-checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">村庄名称</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">所属地市</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">区县</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">聚类类别</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">综合指数</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">风险等级</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 15%;">主要用能短板</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">发展潜力</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">当前状态</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 8%;">操作</th>
            </tr>
          </thead>
          <tbody id="villages-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors" data-status="pending">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border village-checkbox" data-village="梅花村">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">梅花村</div>
                <div class="text-sm text-text-secondary mt-1">梅花镇</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">城口县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-4">待提升村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-danger">65.8</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-high">高风险</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">产业用电不足、基础设施用电缺口大</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="potential-badge badge-medium-potential">中等潜力</span></td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-pending">待筛选</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-success hover:text-success/80 transition-colors" title="设为候选" onclick="updateStatus(this, 'candidate')">
                    <i class="fa fa-star"></i>
                  </button>
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors" data-status="candidate">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border village-checkbox" data-village="石桥村">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">石桥村</div>
                <div class="text-sm text-text-secondary mt-1">石桥乡</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">巫溪县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-3">平衡型村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-warning">78.9</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-medium">中风险</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">农业用电效率低、清洁能源利用不足</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="potential-badge badge-high-potential">高潜力</span></td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-candidate">候选</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="选定帮扶" onclick="updateStatus(this, 'selected')">
                    <i class="fa fa-check"></i>
                  </button>
                  <button class="text-warning hover:text-warning/80 transition-colors" title="取消候选" onclick="updateStatus(this, 'pending')">
                    <i class="fa fa-times"></i>
                  </button>
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors" data-status="selected">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border village-checkbox" data-village="青山村">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">青山村</div>
                <div class="text-sm text-text-secondary mt-1">青山镇</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">奉节县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-2">成长型村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-primary">85.2</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-low">低风险</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">产业用电结构需优化、智能化程度待提升</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="potential-badge badge-high-potential">高潜力</span></td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-selected">已选定</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-success hover:text-success/80 transition-colors" title="制定帮扶计划">
                    <i class="fa fa-tasks"></i>
                  </button>
                  <button class="text-warning hover:text-warning/80 transition-colors" title="取消选定" onclick="updateStatus(this, 'candidate')">
                    <i class="fa fa-undo"></i>
                  </button>
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors" data-status="pending">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border village-checkbox" data-village="桃花村">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">桃花村</div>
                <div class="text-sm text-text-secondary mt-1">桃花乡</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">城口县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-4">待提升村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-danger">62.3</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-high">高风险</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">电网覆盖不足、用电质量不稳定</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="potential-badge badge-low-potential">低潜力</span></td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-pending">待筛选</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-success hover:text-success/80 transition-colors" title="设为候选" onclick="updateStatus(this, 'candidate')">
                    <i class="fa fa-star"></i>
                  </button>
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors" data-status="candidate">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border village-checkbox" data-village="竹林村">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">竹林村</div>
                <div class="text-sm text-text-secondary mt-1">竹林乡</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">云阳县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-5">特色型村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-accent">82.3</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-low">低风险</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">旅游配套用电设施不完善</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="potential-badge badge-high-potential">高潜力</span></td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-candidate">候选</span></td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="选定帮扶" onclick="updateStatus(this, 'selected')">
                    <i class="fa fa-check"></i>
                  </button>
                  <button class="text-warning hover:text-warning/80 transition-colors" title="取消候选" onclick="updateStatus(this, 'pending')">
                    <i class="fa fa-times"></i>
                  </button>
                  <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 115 条，每页显示 10 条，第 1 页/共 12 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">2</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">3</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 批量操作面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <h3 class="text-xl font-semibold text-text-primary mb-4">批量操作</h3>
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
          <span class="text-text-secondary">已选择:</span>
          <span id="selected-count" class="font-semibold text-primary">0</span>
          <span class="text-text-secondary">个村庄</span>
        </div>
        <div class="flex space-x-3">
          <button id="btn-batch-candidate" class="px-4 py-2 bg-warning text-white rounded-lg border border-warning hover:bg-warning/90 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-star mr-1"></i> 批量设为候选
          </button>
          <button id="btn-batch-select" class="px-4 py-2 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-check mr-1"></i> 批量选定帮扶
          </button>
          <button id="btn-batch-reset" class="px-4 py-2 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-undo mr-1"></i> 批量重置状态
          </button>
          <button id="btn-batch-export" class="px-4 py-2 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-download mr-1"></i> 导出选中
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 状态更新函数
      window.updateStatus = function(button, newStatus) {
        const row = button.closest('tr');
        const villageName = row.querySelector('.font-medium').textContent;
        const statusCell = row.querySelector('.status-badge');
        const actionsCell = row.querySelector('td:last-child > div');

        // 更新状态显示
        statusCell.className = 'status-badge';
        row.dataset.status = newStatus;

        switch(newStatus) {
          case 'pending':
            statusCell.classList.add('badge-pending');
            statusCell.textContent = '待筛选';
            actionsCell.innerHTML = `
              <button class="text-success hover:text-success/80 transition-colors" title="设为候选" onclick="updateStatus(this, 'candidate')">
                <i class="fa fa-star"></i>
              </button>
              <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                <i class="fa fa-eye"></i>
              </button>
            `;
            break;
          case 'candidate':
            statusCell.classList.add('badge-candidate');
            statusCell.textContent = '候选';
            actionsCell.innerHTML = `
              <button class="text-primary hover:text-primary/80 transition-colors" title="选定帮扶" onclick="updateStatus(this, 'selected')">
                <i class="fa fa-check"></i>
              </button>
              <button class="text-warning hover:text-warning/80 transition-colors" title="取消候选" onclick="updateStatus(this, 'pending')">
                <i class="fa fa-times"></i>
              </button>
              <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                <i class="fa fa-eye"></i>
              </button>
            `;
            break;
          case 'selected':
            statusCell.classList.add('badge-selected');
            statusCell.textContent = '已选定';
            actionsCell.innerHTML = `
              <button class="text-success hover:text-success/80 transition-colors" title="制定帮扶计划">
                <i class="fa fa-tasks"></i>
              </button>
              <button class="text-warning hover:text-warning/80 transition-colors" title="取消选定" onclick="updateStatus(this, 'candidate')">
                <i class="fa fa-undo"></i>
              </button>
              <button class="text-primary hover:text-primary/80 transition-colors" title="查看详情">
                <i class="fa fa-eye"></i>
              </button>
            `;
            break;
        }

        alert(`${villageName} 状态已更新为：${statusCell.textContent}`);
        updateStatistics();
      };

      // 更新统计数据
      function updateStatistics() {
        const rows = document.querySelectorAll('#villages-table-body tr');
        let pendingCount = 0;
        let candidateCount = 0;
        let selectedCount = 0;

        rows.forEach(row => {
          const status = row.dataset.status;
          switch(status) {
            case 'pending': pendingCount++; break;
            case 'candidate': candidateCount++; break;
            case 'selected': selectedCount++; break;
          }
        });

        console.log('统计更新:', { pendingCount, candidateCount, selectedCount });
      }

      // 全选功能
      const selectAllCheckbox = document.getElementById('select-all-checkbox');
      const villageCheckboxes = document.querySelectorAll('.village-checkbox');

      selectAllCheckbox.addEventListener('change', function() {
        villageCheckboxes.forEach(checkbox => {
          checkbox.checked = this.checked;
        });
        updateSelectedCount();
      });

      // 单个复选框变化
      villageCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          updateSelectedCount();

          // 更新全选状态
          const checkedCount = document.querySelectorAll('.village-checkbox:checked').length;
          selectAllCheckbox.checked = checkedCount === villageCheckboxes.length;
          selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < villageCheckboxes.length;
        });
      });

      // 更新选中数量
      function updateSelectedCount() {
        const checkedCount = document.querySelectorAll('.village-checkbox:checked').length;
        document.getElementById('selected-count').textContent = checkedCount;

        // 更新批量操作按钮状态
        const batchButtons = document.querySelectorAll('#btn-batch-candidate, #btn-batch-select, #btn-batch-reset, #btn-batch-export');
        batchButtons.forEach(button => {
          button.disabled = checkedCount === 0;
        });
      }

      // 状态筛选
      document.getElementById('status-filter').addEventListener('change', function() {
        const selectedStatus = this.value;
        const rows = document.querySelectorAll('#villages-table-body tr');

        rows.forEach(row => {
          if (selectedStatus === 'all' || row.dataset.status === selectedStatus) {
            row.style.display = '';
          } else {
            row.style.display = 'none';
          }
        });
      });

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function() {
        const searchParams = {
          villageName: document.getElementById('village-name').value,
          city: document.getElementById('city-select').value,
          district: document.getElementById('district-select').value,
          riskLevel: document.getElementById('risk-level').value,
          clusterType: document.getElementById('cluster-type').value,
          scoreMin: document.getElementById('score-min').value,
          scoreMax: document.getElementById('score-max').value,
          currentStatus: document.getElementById('current-status').value
        };

        console.log('查询条件:', searchParams);
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置按钮事件
      document.getElementById('btn-reset').addEventListener('click', function() {
        document.getElementById('village-name').value = '';
        document.getElementById('city-select').value = '';
        document.getElementById('district-select').value = '';
        document.getElementById('risk-level').value = '';
        document.getElementById('cluster-type').value = '';
        document.getElementById('score-min').value = '';
        document.getElementById('score-max').value = '';
        document.getElementById('current-status').value = '';
        document.getElementById('status-filter').value = 'all';

        // 显示所有行
        document.querySelectorAll('#villages-table-body tr').forEach(row => {
          row.style.display = '';
        });
      });

      // 高级筛选按钮
      document.getElementById('btn-advanced-filter').addEventListener('click', function() {
        alert('高级筛选\n\n这里将打开高级筛选面板，支持更复杂的筛选条件组合');
      });

      // 导出列表按钮
      document.getElementById('btn-export-list').addEventListener('click', function() {
        alert('导出村庄列表\n\n将生成包含所有筛选结果的Excel文件');
      });

      // 批量操作按钮
      document.getElementById('btn-batch-operation').addEventListener('click', function() {
        alert('批量操作\n\n这里将显示批量操作选项面板');
      });

      // 批量设为候选
      document.getElementById('btn-batch-candidate').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.village-checkbox:checked');
        if (checkedBoxes.length === 0) return;

        if (confirm(`确定要将选中的 ${checkedBoxes.length} 个村庄设为候选吗？`)) {
          checkedBoxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row.dataset.status === 'pending') {
              updateStatus(row.querySelector('button[onclick*="candidate"]'), 'candidate');
            }
          });
        }
      });

      // 批量选定帮扶
      document.getElementById('btn-batch-select').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.village-checkbox:checked');
        if (checkedBoxes.length === 0) return;

        if (confirm(`确定要将选中的 ${checkedBoxes.length} 个村庄选定为帮扶对象吗？`)) {
          checkedBoxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (row.dataset.status === 'candidate') {
              updateStatus(row.querySelector('button[onclick*="selected"]'), 'selected');
            }
          });
        }
      });

      // 批量重置状态
      document.getElementById('btn-batch-reset').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.village-checkbox:checked');
        if (checkedBoxes.length === 0) return;

        if (confirm(`确定要重置选中的 ${checkedBoxes.length} 个村庄的状态吗？`)) {
          checkedBoxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            updateStatus(row.querySelector('button'), 'pending');
          });
        }
      });

      // 批量导出
      document.getElementById('btn-batch-export').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.village-checkbox:checked');
        if (checkedBoxes.length === 0) return;

        alert(`批量导出\n\n将导出选中的 ${checkedBoxes.length} 个村庄的详细信息`);
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });

      // 初始化
      updateSelectedCount();
      updateStatistics();
    });
  </script>
</body>

</html>
