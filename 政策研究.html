<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 政策研究 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }

      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }

    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-search text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 政策研究</h1>
          <p class="text-sm text-text-secondary">大模型能源电力政策信息挖掘系统</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 查询条件面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">政策研究项目查询</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">研究主题名称</label>
          <input type="text" id="research-theme" placeholder="请输入研究主题名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">研究类型</label>
          <div class="relative">
            <select id="research-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类型</option>
              <option value="演变分析">演变分析</option>
              <option value="对比分析">对比分析</option>
              <option value="影响评估">影响评估</option>
              <option value="效果研究">效果研究</option>
              <option value="趋势预测">趋势预测</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">关联政策</label>
          <input type="text" id="related-policy" placeholder="请输入关联政策名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">创建日期</label>
          <input type="date" id="create-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
        </div>
        <button id="btn-new-research" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
          <i class="fa fa-plus mr-1"></i> 新建研究项目
        </button>
      </div>
    </div>

    <!-- 研究项目列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">政策研究项目列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full min-w-[1000px] border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">序号</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">研究主题</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">研究类型</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">关联政策</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">创建人</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">创建日期</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary">操作</th>
            </tr>
          </thead>
          <tbody id="research-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 text-text-primary border-r border-border">001</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">乡村振兴能源政策演变分析</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-sm">演变分析</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">《关于实现巩固拓展脱贫攻坚成果同乡村振兴有效衔接的意见》</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">张研究员</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-08-01</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 text-text-primary border-r border-border">002</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">分布式光伏政策效果对比研究</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm">对比分析</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">《关于促进新时代新能源高质量发展的实施方案》</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">李分析师</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-07-28</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 text-text-primary border-r border-border">003</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">电力基础设施投资政策影响评估</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-accent/10 text-accent rounded-full text-sm">影响评估</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">《"十四五"现代能源体系规划》</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">王专家</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-07-25</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 text-text-primary border-r border-border">004</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">农村电网改造升级效果研究</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-warning/10 text-warning rounded-full text-sm">效果研究</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">《农村电网巩固提升工程实施方案》</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">陈工程师</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-07-20</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 text-text-primary border-r border-border">005</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">新能源产业发展趋势预测</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-success/10 text-success rounded-full text-sm">趋势预测</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">《2030年前碳达峰行动方案》</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">刘顾问</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-07-15</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 text-text-primary border-r border-border">006</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">智能电网建设政策对比分析</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm">对比分析</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">《关于推进电力源网荷储一体化和多能互补发展的指导意见》</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">赵主任</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-07-10</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 6 条，每页显示 10 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 新建研究项目模态框 -->
    <div id="new-research-modal" class="fixed inset-0 bg-black/10 flex items-center justify-center z-50 hidden">
      <div class="bg-lighter border border-border rounded-xl p-8 w-full max-w-2xl transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-2xl font-semibold text-text-primary">新建研究项目</h3>
          <button id="close-modal" class="text-text-secondary hover:text-primary transition-colors">
            <i class="fa fa-times text-xl"></i>
          </button>
        </div>

        <form class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-text-secondary block mb-2">研究主题名称 *</label>
              <input type="text" id="new-theme" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入研究主题名称">
            </div>
            <div>
              <label class="text-text-secondary block mb-2">研究类型 *</label>
              <select id="new-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
                <option value="">请选择研究类型</option>
                <option value="演变分析">演变分析</option>
                <option value="对比分析">对比分析</option>
                <option value="影响评估">影响评估</option>
                <option value="效果研究">效果研究</option>
                <option value="趋势预测">趋势预测</option>
              </select>
            </div>
          </div>

          <div>
            <label class="text-text-secondary block mb-2">关联政策 *</label>
            <input type="text" id="new-policy" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入关联政策名称">
          </div>

          <div>
            <label class="text-text-secondary block mb-2">研究描述</label>
            <textarea id="new-description" rows="4" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请输入研究项目的详细描述..."></textarea>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-text-secondary block mb-2">预计开始时间</label>
              <input type="date" id="new-start-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            </div>
            <div>
              <label class="text-text-secondary block mb-2">预计结束时间</label>
              <input type="date" id="new-end-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            </div>
          </div>
        </form>

        <div class="flex justify-end space-x-4 mt-6">
          <button id="cancel-new-research" class="px-6 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            取消
          </button>
          <button id="confirm-new-research" class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            创建项目
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      document.getElementById('create-date').value = formattedDate;

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const researchTheme = document.getElementById('research-theme').value;
        const researchType = document.getElementById('research-type').value;
        const relatedPolicy = document.getElementById('related-policy').value;
        const createDate = document.getElementById('create-date').value;

        console.log('查询条件:', {
          researchTheme,
          researchType,
          relatedPolicy,
          createDate
        });

        // 这里可以添加实际的查询逻辑
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置按钮事件
      document.getElementById('btn-reset').addEventListener('click', function () {
        document.getElementById('research-theme').value = '';
        document.getElementById('research-type').value = '';
        document.getElementById('related-policy').value = '';
        document.getElementById('create-date').value = formattedDate;
      });

      // 新建研究项目按钮
      document.getElementById('btn-new-research').addEventListener('click', function () {
        const modal = document.getElementById('new-research-modal');
        const modalContent = document.getElementById('modal-content');

        modal.classList.remove('hidden');
        // 设置默认日期
        document.getElementById('new-start-date').value = formattedDate;

        // 添加动画效果
        setTimeout(() => {
          modalContent.classList.remove('scale-95', 'opacity-0');
          modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
      });

      // 关闭模态框
      document.getElementById('close-modal').addEventListener('click', function () {
        closeModal();
      });

      document.getElementById('cancel-new-research').addEventListener('click', function () {
        closeModal();
      });

      // 确认创建研究项目
      document.getElementById('confirm-new-research').addEventListener('click', function () {
        const theme = document.getElementById('new-theme').value;
        const type = document.getElementById('new-type').value;
        const policy = document.getElementById('new-policy').value;

        if (!theme || !type || !policy) {
          alert('请填写必填项：研究主题名称、研究类型、关联政策');
          return;
        }

        console.log('新建研究项目:', {
          theme,
          type,
          policy,
          description: document.getElementById('new-description').value,
          startDate: document.getElementById('new-start-date').value,
          endDate: document.getElementById('new-end-date').value
        });

        alert('研究项目创建成功！');
        closeModal();
        // 这里可以添加实际的创建逻辑，比如刷新表格数据
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-eye')) {
          const row = e.target.closest('tr');
          const theme = row.cells[1].textContent;
          alert(`查看研究项目详情：${theme}\n\n这里将显示政策时间轴、对比图等可视化详情`);
        }

        if (e.target.closest('.fa-edit')) {
          const row = e.target.closest('tr');
          const theme = row.cells[1].textContent;
          alert(`编辑研究项目：${theme}`);
        }

        if (e.target.closest('.fa-trash')) {
          const row = e.target.closest('tr');
          const theme = row.cells[1].textContent;
          if (confirm(`确定要删除研究项目"${theme}"吗？`)) {
            alert('研究项目已删除');
            // 这里可以添加实际的删除逻辑
          }
        }
      });

      // 辅助函数：关闭模态框
      function closeModal() {
        const modal = document.getElementById('new-research-modal');
        const modalContent = document.getElementById('modal-content');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
          modal.classList.add('hidden');
          // 清空表单
          document.getElementById('new-theme').value = '';
          document.getElementById('new-type').value = '';
          document.getElementById('new-policy').value = '';
          document.getElementById('new-description').value = '';
          document.getElementById('new-start-date').value = '';
          document.getElementById('new-end-date').value = '';
        }, 300);
      }

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
