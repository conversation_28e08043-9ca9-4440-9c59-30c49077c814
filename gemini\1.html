<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>台区电量异常信息统计</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-bolt text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary"></span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-end">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">管理单位</label>
          <div class="relative">
            <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部单位</option>
              <option value="hohhot">呼和浩特市</option>
              <option value="baotou">包头市</option>
              <option value="ordos">鄂尔多斯市</option>
              <option value="chifeng">赤峰市</option>
              <option value="hulunbuir">呼伦贝尔市</option>
              <option value="tongliao">通辽市</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
            <label class="text-text-secondary mb-2">台区编号/名称</label>
            <input type="text" placeholder="请输入编号或名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
            <label class="text-text-secondary mb-2">异常类型</label>
            <div class="relative">
              <select class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="all">全部类型</option>
                <option value="type1">电量突降</option>
                <option value="type2">通讯中断</option>
                <option value="type3">电压越限</option>
                <option value="type4">持续零电量</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
        </div>

        <div class="flex flex-col">
            <label class="text-text-secondary mb-2">统计日期</label>
            <div class="flex space-x-3">
              <input type="date" id="main-date" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" style="min-width: 120px;">
              <button id="btn-search" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors flex-shrink-0">
                <i class="fa fa-search mr-1"></i> 查询
              </button>
            </div>
        </div>
      </div>
    </div>

    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h2 class="text-2xl font-semibold text-text-primary mb-6">电量异常信息列表</h2>
        <div class="overflow-x-auto">
            <table id="results-table" class="table-auto w-full border-collapse border border-border text-sm">
                <thead class="bg-light">
                    <tr>
                        <th class="p-3 font-semibold text-left border border-border text-text-secondary">管理单位</th>
                        <th class="p-3 font-semibold text-left border border-border text-text-secondary">台区编号</th>
                        <th class="p-3 font-semibold text-left border border-border text-text-secondary">台区名称</th>
                        <th class="p-3 font-semibold text-left border border-border text-text-secondary">线路编号</th>
                        <th class="p-3 font-semibold text-left border border-border text-text-secondary">线路名称</th>
                        <th class="p-3 font-semibold text-right border border-border text-text-secondary">装机容量(kW)</th>
                        <th class="p-3 font-semibold text-left border border-border text-text-secondary">异常类型</th>
                        <th class="p-3 font-semibold text-center border border-border text-text-secondary">异常发生时间</th>
                        <th class="p-3 font-semibold text-center border border-border text-text-secondary">异常恢复时间</th>
                        <th class="p-3 font-semibold text-center border border-border text-text-secondary">异常持续时间</th>
                    </tr>
                </thead>
                <tbody class="text-text-primary">
                    <tr class="hover:bg-light/50">
                        <td class="p-3 border border-border">呼和浩特市</td>
                        <td class="p-3 border border-border">TQ-HHHT-001</td>
                        <td class="p-3 border border-border">新华东街台区</td>
                        <td class="p-3 border border-border">XL-XHD-01</td>
                        <td class="p-3 border border-border">新华一号线</td>
                        <td class="p-3 text-right border border-border">150.0</td>
                        <td class="p-3 border border-border"><span class="bg-warning/20 text-warning-darker px-2 py-1 rounded-full text-xs font-semibold">电量突降</span></td>
                        <td class="p-3 text-center border border-border">2025-07-16 10:30:15</td>
                        <td class="p-3 text-center border border-border">2025-07-16 11:05:45</td>
                        <td class="p-3 text-center border border-border">35分钟30秒</td>
                    </tr>
                    <tr class="hover:bg-light/50">
                        <td class="p-3 border border-border">包头市</td>
                        <td class="p-3 border border-border">TQ-BT-042</td>
                        <td class="p-3 border border-border">钢铁大街2号台区</td>
                        <td class="p-3 border border-border">XL-GT-02</td>
                        <td class="p-3 border border-border">钢铁大街乙线</td>
                        <td class="p-3 text-right border border-border">300.5</td>
                        <td class="p-3 border border-border"><span class="bg-danger/20 text-danger-darker px-2 py-1 rounded-full text-xs font-semibold">通讯中断</span></td>
                        <td class="p-3 text-center border border-border">2025-07-16 08:00:00</td>
                        <td class="p-3 text-center border border-border">2025-07-16 14:20:10</td>
                        <td class="p-3 text-center border border-border">6小时20分10秒</td>
                    </tr>
                    <tr class="hover:bg-light/50">
                        <td class="p-3 border border-border">鄂尔多斯市</td>
                        <td class="p-3 border border-border">TQ-EEDS-101</td>
                        <td class="p-3 border border-border">康巴什新区台区</td>
                        <td class="p-3 border border-border">XL-KBS-05</td>
                        <td class="p-3 border border-border">康巴什政务线</td>
                        <td class="p-3 text-right border border-border">250.0</td>
                        <td class="p-3 border border-border"><span class="bg-accent/20 text-accent-darker px-2 py-1 rounded-full text-xs font-semibold">电压越限</span></td>
                        <td class="p-3 text-center border border-border">2025-07-16 13:45:05</td>
                        <td class="p-3 text-center border border-border">2025-07-16 13:47:35</td>
                        <td class="p-3 text-center border border-border">2分钟30秒</td>
                    </tr>
                    <tr class="hover:bg-light/50">
                        <td class="p-3 border border-border">赤峰市</td>
                        <td class="p-3 border border-border">TQ-CF-088</td>
                        <td class="p-3 border border-border">红山区振兴街台区</td>
                        <td class="p-3 border border-border">XL-HS-11</td>
                        <td class="p-3 border border-border">振兴街配线</td>
                        <td class="p-3 text-right border border-border">180.8</td>
                        <td class="p-3 border border-border"><span class="bg-secondary/20 text-secondary-darker px-2 py-1 rounded-full text-xs font-semibold">持续零电量</span></td>
                        <td class="p-3 text-center border border-border">2025-07-15 18:10:20</td>
                        <td class="p-3 text-center border border-border">2025-07-16 09:00:00</td>
                        <td class="p-3 text-center border border-border">14小时49分40秒</td>
                    </tr>
                    <tr class="hover:bg-light/50">
                        <td class="p-3 border border-border">呼伦贝尔市</td>
                        <td class="p-3 border border-border">TQ-HLBE-015</td>
                        <td class="p-3 border border-border">海拉尔河西台区</td>
                        <td class="p-3 border border-border">XL-HLR-03</td>
                        <td class="p-3 border border-border">河西三号线</td>
                        <td class="p-3 text-right border border-border">220.3</td>
                        <td class="p-3 border border-border"><span class="bg-warning/20 text-warning-darker px-2 py-1 rounded-full text-xs font-semibold">电量突降</span></td>
                        <td class="p-3 text-center border border-border">2025-07-16 15:02:50</td>
                        <td class="p-3 text-center border border-border">2025-07-16 16:10:00</td>
                        <td class="p-3 text-center border border-border">1小时7分10秒</td>
                    </tr>
                    <tr class="hover:bg-light/50">
                        <td class="p-3 border border-border">通辽市</td>
                        <td class="p-3 border border-border">TQ-TL-029</td>
                        <td class="p-3 border border-border">科尔沁区明仁大街台区</td>
                        <td class="p-3 border border-border">XL-KEQ-08</td>
                        <td class="p-3 border border-border">明仁大街专线</td>
                        <td class="p-3 text-right border border-border">450.0</td>
                        <td class="p-3 border border-border"><span class="bg-danger/20 text-danger-darker px-2 py-1 rounded-full text-xs font-semibold">通讯中断</span></td>
                        <td class="p-3 text-center border border-border">2025-07-16 11:55:10</td>
                        <td class="p-3 text-center border border-border">2025-07-16 12:30:15</td>
                        <td class="p-3 text-center border border-border">35分钟5秒</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="flex flex-col sm:flex-row justify-between items-center mt-6">
            <span class="text-sm text-text-secondary mb-4 sm:mb-0">
              共 25 条记录，当前显示第 1 - 6 条
            </span>
            <div class="flex items-center space-x-1">
              <button class="px-3 py-1 rounded-lg text-text-secondary hover:bg-light/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                <i class="fa fa-chevron-left"></i>
              </button>
              <button class="w-8 h-8 rounded-lg bg-primary text-white transition-colors">1</button>
              <button class="w-8 h-8 rounded-lg text-text-secondary hover:bg-light/80 transition-colors">2</button>
              <button class="w-8 h-8 rounded-lg text-text-secondary hover:bg-light/80 transition-colors">3</button>
              <button class="w-8 h-8 rounded-lg text-text-secondary hover:bg-light/80 transition-colors">4</button>
              <button class="w-8 h-8 rounded-lg text-text-secondary hover:bg-light/80 transition-colors">5</button>
              <button class="px-3 py-1 rounded-lg text-text-secondary hover:bg-light/80 transition-colors">
                <i class="fa fa-chevron-right"></i>
              </button>
            </div>
        </div>
    </div>
  </div>

  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      const mainDateInput = document.getElementById('main-date');
      if (mainDateInput) {
        mainDateInput.value = formattedDate;
      }

      // 在页眉显示当前日期
      const dateDisplay = document.getElementById('current-date-display');
      if(dateDisplay) {
        const year = today.getFullYear();
        const month = today.getMonth() + 1;
        const day = today.getDate();
        dateDisplay.textContent = `${year}年${month}月${day}日`;
      }
      
      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>