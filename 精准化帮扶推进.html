<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 精准化帮扶推进 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-not-started { background-color: rgba(148, 163, 184, 0.1); color: #94A3B8; }
      .badge-in-progress { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-completed { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-delayed { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .priority-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-high { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .badge-medium { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-low { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .progress-bar {
        height: 8px;
        background-color: #E2E8F0;
        border-radius: 4px;
        overflow: hidden;
      }
      .progress-fill {
        height: 100%;
        transition: width 0.3s ease;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-tasks text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 精准化帮扶推进</h1>
          <p class="text-sm text-text-secondary">对已选定帮扶对象进行任务分解和全流程跟踪管理</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">
    <!-- 帮扶推进统计概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">帮扶任务总数</p>
            <h3 class="text-3xl font-bold mt-2">48 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-list-alt text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 15.2%
          </span>
          <span class="text-text-secondary ml-2">较上月增加</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">进行中任务</p>
            <h3 class="text-3xl font-bold mt-2">28 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-spinner text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-warning flex items-center">
            <i class="fa fa-clock-o mr-1"></i> 进行中
          </span>
          <span class="text-text-secondary ml-2">58.3%占比</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">已完成任务</p>
            <h3 class="text-3xl font-bold mt-2">15 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-check mr-1"></i> 31.3%
          </span>
          <span class="text-text-secondary ml-2">完成率</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">平均完成度</p>
            <h3 class="text-3xl font-bold mt-2">72.5 <span class="text-lg font-normal text-text-secondary">%</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-bar-chart text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 8.7%
          </span>
          <span class="text-text-secondary ml-2">较上月提升</span>
        </div>
      </div>
    </div>

    <!-- 帮扶任务查询面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">帮扶任务查询</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">帮扶村庄</label>
          <input type="text" id="village-name" placeholder="请输入村庄名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">任务状态</label>
          <div class="relative">
            <select id="task-status" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部状态</option>
              <option value="未开始">未开始</option>
              <option value="进行中">进行中</option>
              <option value="已完成">已完成</option>
              <option value="延期">延期</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">责任单位</label>
          <div class="relative">
            <select id="responsible-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部单位</option>
              <option value="国网重庆电力">国网重庆电力</option>
              <option value="重庆市发改委">重庆市发改委</option>
              <option value="重庆市农业农村委">重庆市农业农村委</option>
              <option value="当地政府">当地政府</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">任务类型</label>
          <div class="relative">
            <select id="task-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类型</option>
              <option value="基础设施建设">基础设施建设</option>
              <option value="产业发展">产业发展</option>
              <option value="技术培训">技术培训</option>
              <option value="政策扶持">政策扶持</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search-tasks" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset-search" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
          <button id="btn-advanced-search" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-sliders mr-1"></i> 高级查询
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-export-tasks" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出任务
          </button>
          <button id="btn-new-task" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
            <i class="fa fa-plus mr-1"></i> 新建任务
          </button>
        </div>
      </div>
    </div>

    <!-- 帮扶成效监控 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 用能数据变化趋势 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">用能数据变化趋势</h3>
          <div class="flex items-center space-x-2">
            <select id="village-selector" class="bg-light border border-border rounded px-3 py-1 text-sm">
              <option value="青山村">青山村</option>
              <option value="石桥村">石桥村</option>
              <option value="竹林村">竹林村</option>
            </select>
            <button class="text-primary hover:text-primary/80 transition-colors">
              <i class="fa fa-expand"></i>
            </button>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="energy-trend-chart"></canvas>
        </div>
      </div>

      <!-- 帮扶成效指标 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-6">帮扶成效指标</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center p-4 bg-light rounded-lg">
            <div>
              <div class="font-medium text-text-primary">产业用电量增长</div>
              <div class="text-sm text-text-secondary">较帮扶前提升</div>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-success">+28.5%</div>
              <div class="text-sm text-text-secondary">显著改善</div>
            </div>
          </div>

          <div class="flex justify-between items-center p-4 bg-light rounded-lg">
            <div>
              <div class="font-medium text-text-primary">居民用电稳定性</div>
              <div class="text-sm text-text-secondary">停电次数减少</div>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-success">-65.2%</div>
              <div class="text-sm text-text-secondary">大幅改善</div>
            </div>
          </div>

          <div class="flex justify-between items-center p-4 bg-light rounded-lg">
            <div>
              <div class="font-medium text-text-primary">综合指数提升</div>
              <div class="text-sm text-text-secondary">脱贫成效评分</div>
            </div>
            <div class="text-right">
              <div class="text-2xl font-bold text-primary">+12.8</div>
              <div class="text-sm text-text-secondary">持续提升</div>
            </div>
          </div>

          <div class="flex justify-between items-center p-4 bg-light rounded-lg">
            <div>
              <div class="font-medium text-text-primary">返贫风险等级</div>
              <div class="text-sm text-text-secondary">风险评估变化</div>
            </div>
            <div class="text-right">
              <div class="text-lg font-bold text-success">中→低</div>
              <div class="text-sm text-text-secondary">风险降低</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 帮扶任务列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">帮扶任务列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-warning/20 text-warning rounded-lg border border-warning/30 hover:bg-warning/30 transition-colors">
            <i class="fa fa-clock-o mr-1"></i> 待办任务
          </button>
          <button class="px-4 py-2 bg-success/20 text-success rounded-lg border border-success/30 hover:bg-success/30 transition-colors">
            <i class="fa fa-check mr-1"></i> 已完成
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 5%;">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">任务编号</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">帮扶村庄</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 20%;">具体帮扶措施</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">启动日期</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">计划完成</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">责任单位</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">当前进度</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">状态</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">成效跟踪数据链接</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 9%;">操作</th>
            </tr>
          </thead>
          <tbody id="tasks-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">TASK-2025-001</div>
                <div class="text-sm text-text-secondary mt-1">基础设施</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">青山村</div>
                <div class="text-sm text-text-secondary mt-1">奉节县</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">完善农村电网基础设施，新建10kV线路3.2公里，改造配电变压器5台</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-07-15</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-09-30</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">国网重庆电力</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex items-center space-x-2">
                  <div class="progress-bar flex-1">
                    <div class="progress-fill bg-success" style="width: 75%"></div>
                  </div>
                  <span class="text-sm font-medium">75%</span>
                </div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-in-progress">进行中</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex flex-col space-y-1">
                  <a href="#" class="text-primary hover:text-primary/80 text-sm underline">用电量监测数据</a>
                  <a href="#" class="text-success hover:text-success/80 text-sm underline">设施建设进度</a>
                  <a href="#" class="text-accent hover:text-accent/80 text-sm underline">综合指数变化</a>
                </div>
              </td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="更新进度">
                    <i class="fa fa-edit"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="查看成效">
                    <i class="fa fa-line-chart"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">TASK-2025-002</div>
                <div class="text-sm text-text-secondary mt-1">产业发展</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">石桥村</div>
                <div class="text-sm text-text-secondary mt-1">巫溪县</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">建设农产品深加工厂房，配套电力设施，提供技术培训和设备支持</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-08-01</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-12-31</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市农业农村委</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex items-center space-x-2">
                  <div class="progress-bar flex-1">
                    <div class="progress-fill bg-warning" style="width: 45%"></div>
                  </div>
                  <span class="text-sm font-medium">45%</span>
                </div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-in-progress">进行中</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex flex-col space-y-1">
                  <a href="#" class="text-primary hover:text-primary/80 text-sm underline">产业用电数据</a>
                  <a href="#" class="text-success hover:text-success/80 text-sm underline">加工厂建设进度</a>
                  <a href="#" class="text-accent hover:text-accent/80 text-sm underline">培训效果评估</a>
                </div>
              </td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="更新进度">
                    <i class="fa fa-edit"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="查看成效">
                    <i class="fa fa-line-chart"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">TASK-2025-003</div>
                <div class="text-sm text-text-secondary mt-1">技术培训</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">竹林村</div>
                <div class="text-sm text-text-secondary mt-1">云阳县</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">开展乡村旅游从业人员培训，提升服务质量和电子商务能力</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-06-20</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-08-20</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">当地政府</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex items-center space-x-2">
                  <div class="progress-bar flex-1">
                    <div class="progress-fill bg-success" style="width: 100%"></div>
                  </div>
                  <span class="text-sm font-medium">100%</span>
                </div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-completed">已完成</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex flex-col space-y-1">
                  <a href="#" class="text-primary hover:text-primary/80 text-sm underline">旅游收入统计</a>
                  <a href="#" class="text-success hover:text-success/80 text-sm underline">培训效果评估</a>
                  <a href="#" class="text-accent hover:text-accent/80 text-sm underline">电商销售数据</a>
                </div>
              </td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="更新进度">
                    <i class="fa fa-edit"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="查看成效">
                    <i class="fa fa-line-chart"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">TASK-2025-004</div>
                <div class="text-sm text-text-secondary mt-1">政策扶持</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">梅花村</div>
                <div class="text-sm text-text-secondary mt-1">城口县</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">制定专项扶持政策，提供电价优惠和产业发展资金支持</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-08-10</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-10-10</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市发改委</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex items-center space-x-2">
                  <div class="progress-bar flex-1">
                    <div class="progress-fill bg-text-tertiary" style="width: 0%"></div>
                  </div>
                  <span class="text-sm font-medium">0%</span>
                </div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-not-started">未开始</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex flex-col space-y-1">
                  <a href="#" class="text-text-tertiary text-sm underline">政策制定进度</a>
                  <a href="#" class="text-text-tertiary text-sm underline">资金拨付情况</a>
                  <a href="#" class="text-text-tertiary text-sm underline">电价优惠执行</a>
                </div>
              </td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="更新进度">
                    <i class="fa fa-edit"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="查看成效">
                    <i class="fa fa-line-chart"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">TASK-2025-005</div>
                <div class="text-sm text-text-secondary mt-1">基础设施</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">桃花村</div>
                <div class="text-sm text-text-secondary mt-1">城口县</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">改善电网供电质量，安装智能电表，建设微电网示范项目</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">2025-07-01</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm text-danger">2025-08-01</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">国网重庆电力</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex items-center space-x-2">
                  <div class="progress-bar flex-1">
                    <div class="progress-fill bg-danger" style="width: 60%"></div>
                  </div>
                  <span class="text-sm font-medium">60%</span>
                </div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="status-badge badge-delayed">延期</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="flex flex-col space-y-1">
                  <a href="#" class="text-primary hover:text-primary/80 text-sm underline">供电质量监测</a>
                  <a href="#" class="text-success hover:text-success/80 text-sm underline">智能电表数据</a>
                  <a href="#" class="text-warning hover:text-warning/80 text-sm underline">微电网建设进度</a>
                </div>
              </td>
              <td class="px-4 py-4">
                <div class="flex space-x-1">
                  <button class="text-primary hover:text-primary/80 transition-colors" title="更新进度">
                    <i class="fa fa-edit"></i>
                  </button>
                  <button class="text-success hover:text-success/80 transition-colors" title="查看成效">
                    <i class="fa fa-line-chart"></i>
                  </button>
                  <button class="text-accent hover:text-accent/80 transition-colors" title="查看详情">
                    <i class="fa fa-eye"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 5 条，每页显示 5 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 创建用能数据变化趋势图
      const energyTrendCtx = document.getElementById('energy-trend-chart').getContext('2d');
      const energyTrendChart = new Chart(energyTrendCtx, {
        type: 'line',
        data: {
          labels: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09', '2024-10', '2024-11', '2024-12'],
          datasets: [
            {
              label: '产业用电量(万kWh)',
              data: [45, 48, 52, 58, 62, 68, 75, 82, 88, 95, 102, 108],
              borderColor: '#31969A',
              backgroundColor: 'rgba(49, 150, 154, 0.1)',
              tension: 0.4,
              fill: true
            },
            {
              label: '居民用电量(万kWh)',
              data: [28, 30, 32, 35, 38, 42, 45, 48, 52, 55, 58, 62],
              borderColor: '#10B981',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              tension: 0.4,
              fill: true
            },
            {
              label: '综合指数得分',
              data: [65, 67, 70, 73, 76, 79, 82, 85, 88, 90, 92, 95],
              borderColor: '#E5CE66',
              backgroundColor: 'rgba(229, 206, 102, 0.1)',
              tension: 0.4,
              fill: false,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                usePointStyle: true,
                padding: 20
              }
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            x: {
              display: true,
              title: {
                display: true,
                text: '时间',
                color: '#64748B'
              },
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B'
              }
            },
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              title: {
                display: true,
                text: '用电量(万kWh)',
                color: '#64748B'
              },
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B'
              }
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              title: {
                display: true,
                text: '综合指数得分',
                color: '#64748B'
              },
              grid: {
                drawOnChartArea: false
              },
              ticks: {
                color: '#64748B'
              }
            }
          },
          interaction: {
            mode: 'nearest',
            axis: 'x',
            intersect: false
          }
        }
      });

      // 村庄选择器变化事件
      document.getElementById('village-selector').addEventListener('change', function() {
        const selectedVillage = this.value;
        console.log('选择村庄:', selectedVillage);
        alert(`切换到${selectedVillage}的用能数据趋势`);
      });

      // 查询按钮事件
      document.getElementById('btn-search-tasks').addEventListener('click', function() {
        const searchParams = {
          villageName: document.getElementById('village-name').value,
          taskStatus: document.getElementById('task-status').value,
          responsibleUnit: document.getElementById('responsible-unit').value,
          taskType: document.getElementById('task-type').value
        };

        console.log('查询条件:', searchParams);
        alert('任务查询功能已触发，请查看控制台输出');
      });

      // 重置查询按钮
      document.getElementById('btn-reset-search').addEventListener('click', function() {
        document.getElementById('village-name').value = '';
        document.getElementById('task-status').value = '';
        document.getElementById('responsible-unit').value = '';
        document.getElementById('task-type').value = '';
      });

      // 高级查询按钮
      document.getElementById('btn-advanced-search').addEventListener('click', function() {
        alert('高级查询\n\n这里将打开高级查询面板，支持更复杂的查询条件组合');
      });

      // 导出任务按钮
      document.getElementById('btn-export-tasks').addEventListener('click', function() {
        alert('导出帮扶任务\n\n将生成包含所有任务信息的Excel文件');
      });

      // 新建任务按钮
      document.getElementById('btn-new-task').addEventListener('click', function() {
        alert('新建帮扶任务\n\n这里将打开任务创建表单，支持从发展建议自动生成任务');
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-edit')) {
          const row = e.target.closest('tr');
          const taskId = row.cells[1].querySelector('.font-medium').textContent;
          const currentProgress = row.cells[7].querySelector('.text-sm').textContent;

          const newProgress = prompt(`更新任务进度：${taskId}\n\n当前进度：${currentProgress}\n请输入新的进度百分比（0-100）:`);
          if (newProgress !== null && !isNaN(newProgress) && newProgress >= 0 && newProgress <= 100) {
            // 更新进度条
            const progressBar = row.cells[7].querySelector('.progress-fill');
            const progressText = row.cells[7].querySelector('.text-sm');
            progressBar.style.width = newProgress + '%';
            progressText.textContent = newProgress + '%';

            // 更新进度条颜色
            if (newProgress == 100) {
              progressBar.className = 'progress-fill bg-success';
              row.cells[8].innerHTML = '<span class="status-badge badge-completed">已完成</span>';
            } else if (newProgress > 0) {
              progressBar.className = 'progress-fill bg-warning';
              row.cells[8].innerHTML = '<span class="status-badge badge-in-progress">进行中</span>';
            } else {
              progressBar.className = 'progress-fill bg-text-tertiary';
              row.cells[8].innerHTML = '<span class="status-badge badge-not-started">未开始</span>';
            }

            alert(`任务 ${taskId} 进度已更新为 ${newProgress}%`);
          }
        }

        if (e.target.closest('.fa-line-chart')) {
          const row = e.target.closest('tr');
          const taskId = row.cells[1].querySelector('.font-medium').textContent;
          const villageName = row.cells[2].querySelector('.font-medium').textContent;
          alert(`查看帮扶成效：${taskId}\n\n村庄：${villageName}\n\n这里将显示该任务的详细成效数据和趋势分析`);
        }

        if (e.target.closest('.fa-eye')) {
          const row = e.target.closest('tr');
          const taskId = row.cells[1].querySelector('.font-medium').textContent;
          alert(`查看任务详情：${taskId}\n\n这里将显示任务的完整信息、执行计划和相关文档`);
        }
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
