<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>台区电量异常信息查询系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .table-container {
        border: 1px solid #E2E8F0;
        border-radius: 8px;
        overflow: hidden;
      }
      .table-header {
        background-color: #F8FAFC;
        border-bottom: 1px solid #E2E8F0;
      }
      .table-row {
        border-bottom: 1px solid #E2E8F0;
      }
      .table-row:last-child {
        border-bottom: none;
      }
      .table-cell {
        border-right: 1px solid #E2E8F0;
        padding: 12px 16px;
      }
      .table-cell:last-child {
        border-right: none;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-bolt text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 查询条件面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">台区电量异常信息查询</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">管理单位</label>
          <div class="relative">
            <select id="management-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部单位</option>
              <option value="薛家湾供电公司">薛家湾供电公司</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">台区编号</label>
          <input type="text" id="station-code" placeholder="请输入台区编号" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">台区名称</label>
          <input type="text" id="station-name" placeholder="请输入台区名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">异常类型</label>
          <div class="relative">
            <select id="exception-type" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类型</option>
              <option value="电压异常">电压异常</option>
              <option value="电流异常">电流异常</option>
              <option value="功率异常">功率异常</option>
              <option value="通信异常">通信异常</option>
              <option value="设备故障">设备故障</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">开始日期</label>
          <input type="date" id="start-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">结束日期</label>
          <input type="date" id="end-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col justify-end">
          <div class="flex space-x-3">
            <button id="btn-search" class="flex-1 px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
              <i class="fa fa-search mr-1"></i> 查询
            </button>
            <button id="btn-reset" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
              <i class="fa fa-refresh mr-1"></i> 重置
            </button>
            <button id="btn-export" class="px-4 py-3 bg-secondary/20 text-primary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
              <i class="fa fa-download mr-1"></i> 导出
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="table-container">
        <div class="overflow-x-auto scrollbar-thin">
          <table class="w-full min-w-[1200px]">
            <thead class="table-header">
              <tr>
                <th class="table-cell text-left font-semibold text-text-primary">管理单位</th>
                <th class="table-cell text-left font-semibold text-text-primary">台区编号</th>
                <th class="table-cell text-left font-semibold text-text-primary">台区名称</th>
                <th class="table-cell text-left font-semibold text-text-primary">线路编号</th>
                <th class="table-cell text-left font-semibold text-text-primary">线路名称</th>
                <th class="table-cell text-left font-semibold text-text-primary">装机容量(kW)</th>
                <th class="table-cell text-left font-semibold text-text-primary">异常类型</th>
                <th class="table-cell text-left font-semibold text-text-primary">异常发生时间</th>
                <th class="table-cell text-left font-semibold text-text-primary">异常恢复时间</th>
                <th class="table-cell text-left font-semibold text-text-primary">异常持续时间</th>
              </tr>
            </thead>
            <tbody id="data-table-body">
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ001</td>
                <td class="table-cell text-text-primary">赛罕一台区</td>
                <td class="table-cell text-text-primary">XL001</td>
                <td class="table-cell text-text-primary">赛罕主线</td>
                <td class="table-cell text-text-primary">500</td>
                <td class="table-cell"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">电压异常</span></td>
                <td class="table-cell text-text-primary">2025-08-04 09:15:30</td>
                <td class="table-cell text-text-primary">2025-08-04 10:22:15</td>
                <td class="table-cell text-text-primary">1小时6分45秒</td>
              </tr>
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ002</td>
                <td class="table-cell text-text-primary">九原二台区</td>
                <td class="table-cell text-text-primary">XL002</td>
                <td class="table-cell text-text-primary">九原支线</td>
                <td class="table-cell text-text-primary">320</td>
                <td class="table-cell"><span class="px-2 py-1 bg-warning/10 text-warning rounded-full text-sm">通信异常</span></td>
                <td class="table-cell text-text-primary">2025-08-04 14:30:12</td>
                <td class="table-cell text-text-primary">2025-08-04 14:45:30</td>
                <td class="table-cell text-text-primary">15分18秒</td>
              </tr>
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ003</td>
                <td class="table-cell text-text-primary">康巴什三台区</td>
                <td class="table-cell text-text-primary">XL003</td>
                <td class="table-cell text-text-primary">康巴什环线</td>
                <td class="table-cell text-text-primary">750</td>
                <td class="table-cell"><span class="px-2 py-1 bg-accent/10 text-accent rounded-full text-sm">功率异常</span></td>
                <td class="table-cell text-text-primary">2025-08-03 22:18:45</td>
                <td class="table-cell text-text-primary">2025-08-04 06:30:20</td>
                <td class="table-cell text-text-primary">8小时11分35秒</td>
              </tr>
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ004</td>
                <td class="table-cell text-text-primary">集宁四台区</td>
                <td class="table-cell text-text-primary">XL004</td>
                <td class="table-cell text-text-primary">集宁北线</td>
                <td class="table-cell text-text-primary">420</td>
                <td class="table-cell"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">设备故障</span></td>
                <td class="table-cell text-text-primary">2025-08-03 16:42:10</td>
                <td class="table-cell text-text-primary">2025-08-03 18:15:25</td>
                <td class="table-cell text-text-primary">1小时33分15秒</td>
              </tr>
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ005</td>
                <td class="table-cell text-text-primary">东胜五台区</td>
                <td class="table-cell text-text-primary">XL005</td>
                <td class="table-cell text-text-primary">东胜工业线</td>
                <td class="table-cell text-text-primary">680</td>
                <td class="table-cell"><span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm">电流异常</span></td>
                <td class="table-cell text-text-primary">2025-08-02 11:25:18</td>
                <td class="table-cell text-text-primary">2025-08-02 12:08:42</td>
                <td class="table-cell text-text-primary">43分24秒</td>
              </tr>
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ006</td>
                <td class="table-cell text-text-primary">准格尔六台区</td>
                <td class="table-cell text-text-primary">XL006</td>
                <td class="table-cell text-text-primary">准格尔南线</td>
                <td class="table-cell text-text-primary">550</td>
                <td class="table-cell"><span class="px-2 py-1 bg-warning/10 text-warning rounded-full text-sm">通信异常</span></td>
                <td class="table-cell text-text-primary">2025-08-02 08:12:35</td>
                <td class="table-cell text-text-primary">2025-08-02 08:28:50</td>
                <td class="table-cell text-text-primary">16分15秒</td>
              </tr>
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ007</td>
                <td class="table-cell text-text-primary">达拉特七台区</td>
                <td class="table-cell text-text-primary">XL007</td>
                <td class="table-cell text-text-primary">达拉特西线</td>
                <td class="table-cell text-text-primary">380</td>
                <td class="table-cell"><span class="px-2 py-1 bg-accent/10 text-accent rounded-full text-sm">功率异常</span></td>
                <td class="table-cell text-text-primary">2025-08-01 19:45:22</td>
                <td class="table-cell text-text-primary">2025-08-01 21:12:08</td>
                <td class="table-cell text-text-primary">1小时26分46秒</td>
              </tr>
              <tr class="table-row hover:bg-light/50 transition-colors">
                <td class="table-cell text-text-primary">薛家湾供电公司</td>
                <td class="table-cell text-text-primary">TQ008</td>
                <td class="table-cell text-text-primary">杭锦旗八台区</td>
                <td class="table-cell text-text-primary">XL008</td>
                <td class="table-cell text-text-primary">杭锦旗东线</td>
                <td class="table-cell text-text-primary">620</td>
                <td class="table-cell"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">电压异常</span></td>
                <td class="table-cell text-text-primary">2025-08-01 13:20:15</td>
                <td class="table-cell text-text-primary">2025-08-01 14:55:30</td>
                <td class="table-cell text-text-primary">1小时35分15秒</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 8 条，每页显示 8 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      document.getElementById('start-date').value = formattedDate;
      document.getElementById('end-date').value = formattedDate;

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const managementUnit = document.getElementById('management-unit').value;
        const stationCode = document.getElementById('station-code').value;
        const stationName = document.getElementById('station-name').value;
        const exceptionType = document.getElementById('exception-type').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;

        console.log('查询条件:', {
          managementUnit,
          stationCode,
          stationName,
          exceptionType,
          startDate,
          endDate
        });

        // 这里可以添加实际的查询逻辑
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置按钮事件
      document.getElementById('btn-reset').addEventListener('click', function () {
        document.getElementById('management-unit').value = '';
        document.getElementById('station-code').value = '';
        document.getElementById('station-name').value = '';
        document.getElementById('exception-type').value = '';
        document.getElementById('start-date').value = formattedDate;
        document.getElementById('end-date').value = formattedDate;
      });

      // 导出按钮事件
      document.getElementById('btn-export').addEventListener('click', function () {
        console.log('导出数据');
        alert('导出功能已触发');
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
