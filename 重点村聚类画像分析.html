<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 重点村聚类画像分析 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
      }
      .radar-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .cluster-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-cluster-1 { background-color: rgba(49, 150, 154, 0.1); color: #31969A; }
      .badge-cluster-2 { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-cluster-3 { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-cluster-4 { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .badge-cluster-5 { background-color: rgba(129, 153, 199, 0.1); color: #8199C7; }
      .risk-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-low { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-medium { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-high { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
      .cluster-legend {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: background-color 0.2s;
      }
      .cluster-legend:hover {
        background-color: rgba(49, 150, 154, 0.05);
      }
      .cluster-legend.active {
        background-color: rgba(49, 150, 154, 0.1);
        border: 1px solid #31969A;
      }
      .cluster-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-sitemap text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 重点村聚类画像分析</h1>
          <p class="text-sm text-text-secondary">基于多维评价指标的村庄聚类分析与画像展示</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">
    <!-- 聚类分析可视化 -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
      <!-- 聚类散点图 -->
      <div class="lg:col-span-3 bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">重点村聚类分布图</h3>
          <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
              <label class="text-sm text-text-secondary">X轴:</label>
              <select id="x-axis-select" class="bg-light border border-border rounded px-3 py-1 text-sm">
                <option value="industry">产业发展指数</option>
                <option value="life">生活水平指数</option>
                <option value="population">人口活力指数</option>
              </select>
            </div>
            <div class="flex items-center space-x-2">
              <label class="text-sm text-text-secondary">Y轴:</label>
              <select id="y-axis-select" class="bg-light border border-border rounded px-3 py-1 text-sm">
                <option value="life" selected>生活水平指数</option>
                <option value="industry">产业发展指数</option>
                <option value="population">人口活力指数</option>
              </select>
            </div>
            <button class="text-primary hover:text-primary/80 transition-colors">
              <i class="fa fa-expand"></i>
            </button>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="cluster-scatter-chart"></canvas>
        </div>
      </div>

      <!-- 聚类图例和统计 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h4 class="text-lg font-semibold text-text-primary mb-4">聚类分组</h4>
        <div class="space-y-2 mb-6">
          <div class="cluster-legend active" data-cluster="1">
            <div class="cluster-dot" style="background-color: #31969A;"></div>
            <div class="flex-1">
              <div class="font-medium text-sm">发达型村庄</div>
              <div class="text-xs text-text-secondary">23个村庄</div>
            </div>
          </div>
          <div class="cluster-legend" data-cluster="2">
            <div class="cluster-dot" style="background-color: #10B981;"></div>
            <div class="flex-1">
              <div class="font-medium text-sm">成长型村庄</div>
              <div class="text-xs text-text-secondary">31个村庄</div>
            </div>
          </div>
          <div class="cluster-legend" data-cluster="3">
            <div class="cluster-dot" style="background-color: #E5CE66;"></div>
            <div class="flex-1">
              <div class="font-medium text-sm">平衡型村庄</div>
              <div class="text-xs text-text-secondary">28个村庄</div>
            </div>
          </div>
          <div class="cluster-legend" data-cluster="4">
            <div class="cluster-dot" style="background-color: #E74C3C;"></div>
            <div class="flex-1">
              <div class="font-medium text-sm">待提升村庄</div>
              <div class="text-xs text-text-secondary">18个村庄</div>
            </div>
          </div>
          <div class="cluster-legend" data-cluster="5">
            <div class="cluster-dot" style="background-color: #8199C7;"></div>
            <div class="flex-1">
              <div class="font-medium text-sm">特色型村庄</div>
              <div class="text-xs text-text-secondary">15个村庄</div>
            </div>
          </div>
        </div>

        <div class="border-t border-border pt-4">
          <h5 class="font-medium text-text-primary mb-3">聚类统计</h5>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-text-secondary">总村庄数:</span>
              <span class="font-medium">115个</span>
            </div>
            <div class="flex justify-between">
              <span class="text-text-secondary">聚类数量:</span>
              <span class="font-medium">5类</span>
            </div>
            <div class="flex justify-between">
              <span class="text-text-secondary">聚类质量:</span>
              <span class="font-medium text-success">优秀</span>
            </div>
            <div class="flex justify-between">
              <span class="text-text-secondary">轮廓系数:</span>
              <span class="font-medium">0.78</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 聚类画像分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 多维雷达图 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">聚类画像雷达图</h3>
          <div class="flex items-center space-x-2">
            <span id="selected-cluster-name" class="cluster-badge badge-cluster-1">发达型村庄</span>
            <button class="text-primary hover:text-primary/80 transition-colors">
              <i class="fa fa-expand"></i>
            </button>
          </div>
        </div>
        <div class="radar-container">
          <canvas id="cluster-radar-chart"></canvas>
        </div>
      </div>

      <!-- 核心特征描述 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">核心特征分析</h3>
          <button class="text-primary hover:text-primary/80 transition-colors">
            <i class="fa fa-download"></i>
          </button>
        </div>

        <div id="cluster-features" class="space-y-4">
          <!-- 用能特征 -->
          <div class="border-l-4 border-primary pl-4">
            <h4 class="font-semibold text-text-primary mb-2">用能特征</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              <li>• 产业用电占比高达68.5%，显著高于其他类型村庄</li>
              <li>• 居民用电均值为2,850kWh/户，处于较高水平</li>
              <li>• 用电增长率稳定在15-20%区间，发展势头良好</li>
            </ul>
          </div>

          <!-- 产业结构 -->
          <div class="border-l-4 border-success pl-4">
            <h4 class="font-semibold text-text-primary mb-2">产业结构</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              <li>• 第二、三产业发达，占总产值的85%以上</li>
              <li>• 制造业和服务业为主导产业</li>
              <li>• 产业多样化程度高，抗风险能力强</li>
            </ul>
          </div>

          <!-- 发展水平 -->
          <div class="border-l-4 border-accent pl-4">
            <h4 class="font-semibold text-text-primary mb-2">发展水平</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              <li>• 人均可支配收入达到35,200元，位居前列</li>
              <li>• 就业率高达92%，人口活力充沛</li>
              <li>• 基础设施完善，生活质量优良</li>
            </ul>
          </div>

          <!-- 发展建议 -->
          <div class="border-l-4 border-warning pl-4">
            <h4 class="font-semibold text-text-primary mb-2">发展建议</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              <li>• 继续发挥产业优势，推动高质量发展</li>
              <li>• 加强创新驱动，提升产业技术含量</li>
              <li>• 发挥示范带动作用，助力周边村庄发展</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 查询条件面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">村庄查询条件</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">村庄名称</label>
          <input type="text" id="village-name" placeholder="请输入村庄名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属地市</label>
          <div class="relative">
            <select id="city-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部地市</option>
              <option value="重庆市">重庆市</option>
              <option value="万州区">万州区</option>
              <option value="涪陵区">涪陵区</option>
              <option value="渝中区">渝中区</option>
              <option value="大渡口区">大渡口区</option>
              <option value="江北区">江北区</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">所属区县</label>
          <div class="relative">
            <select id="district-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部区县</option>
              <option value="巫山县">巫山县</option>
              <option value="奉节县">奉节县</option>
              <option value="巫溪县">巫溪县</option>
              <option value="城口县">城口县</option>
              <option value="云阳县">云阳县</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">聚类类别</label>
          <div class="relative">
            <select id="cluster-select" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部类别</option>
              <option value="发达型村庄">发达型村庄</option>
              <option value="成长型村庄">成长型村庄</option>
              <option value="平衡型村庄">平衡型村庄</option>
              <option value="待提升村庄">待提升村庄</option>
              <option value="特色型村庄">特色型村庄</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">综合指数得分范围</label>
          <div class="flex items-center space-x-3">
            <input type="number" id="score-min" placeholder="最低分" min="0" max="100" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            <span class="text-text-secondary">-</span>
            <input type="number" id="score-max" placeholder="最高分" min="0" max="100" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">返贫风险预警等级</label>
          <div class="relative">
            <select id="risk-level" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部等级</option>
              <option value="低风险">低风险</option>
              <option value="中风险">中风险</option>
              <option value="高风险">高风险</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-export" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出数据
          </button>
          <button id="btn-analysis" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-bar-chart mr-1"></i> 深度分析
          </button>
        </div>
      </div>
    </div>

    <!-- 村庄列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">重点村庄列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-success/20 text-success rounded-lg border border-success/30 hover:bg-success/30 transition-colors">
            <i class="fa fa-plus mr-1"></i> 批量分析
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 5%;">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 15%;">村庄名称</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">所属地市</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">所属区县</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">聚类类别</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">综合指数得分</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">产业用电占比</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">居民用电均值</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">返贫风险等级</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 8%;">操作</th>
            </tr>
          </thead>
          <tbody id="village-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">龙门村</div>
                <div class="text-sm text-text-secondary mt-1">龙门镇</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">巫山县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-1">发达型村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-success">92.5</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">68.5%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2,850kWh</td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-low">低风险</span></td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors" title="画像分析">
                  <i class="fa fa-user"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">青山村</div>
                <div class="text-sm text-text-secondary mt-1">青山镇</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">奉节县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-2">成长型村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-primary">85.2</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">52.3%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2,150kWh</td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-low">低风险</span></td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors" title="画像分析">
                  <i class="fa fa-user"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">石桥村</div>
                <div class="text-sm text-text-secondary mt-1">石桥乡</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">巫溪县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-3">平衡型村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-warning">78.9</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">45.7%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">1,920kWh</td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-medium">中风险</span></td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors" title="画像分析">
                  <i class="fa fa-user"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">梅花村</div>
                <div class="text-sm text-text-secondary mt-1">梅花镇</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">城口县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-4">待提升村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-danger">65.8</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">38.2%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">1,650kWh</td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-high">高风险</span></td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors" title="画像分析">
                  <i class="fa fa-user"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">竹林村</div>
                <div class="text-sm text-text-secondary mt-1">竹林乡</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">重庆市</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">云阳县</td>
              <td class="px-4 py-4 border-r border-border"><span class="cluster-badge badge-cluster-5">特色型村庄</span></td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-lg font-bold text-accent">82.3</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">35.8%</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">3,200kWh</td>
              <td class="px-4 py-4 border-r border-border"><span class="risk-badge badge-low">低风险</span></td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors" title="画像分析">
                  <i class="fa fa-user"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 115 条，每页显示 10 条，第 1 页/共 12 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">2</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">3</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 聚类散点图数据
      const clusterData = {
        1: { // 发达型村庄
          data: [
            {x: 85, y: 90, village: '龙门村'},
            {x: 88, y: 92, village: '凤凰村'},
            {x: 82, y: 87, village: '金山村'},
            {x: 90, y: 95, village: '银河村'},
            {x: 86, y: 89, village: '翠竹村'}
          ],
          color: '#31969A',
          name: '发达型村庄'
        },
        2: { // 成长型村庄
          data: [
            {x: 75, y: 78, village: '青山村'},
            {x: 72, y: 80, village: '绿水村'},
            {x: 78, y: 75, village: '春风村'},
            {x: 76, y: 82, village: '朝阳村'},
            {x: 74, y: 77, village: '向阳村'}
          ],
          color: '#10B981',
          name: '成长型村庄'
        },
        3: { // 平衡型村庄
          data: [
            {x: 65, y: 68, village: '石桥村'},
            {x: 68, y: 65, village: '木桥村'},
            {x: 62, y: 70, village: '铁桥村'},
            {x: 70, y: 67, village: '彩虹村'},
            {x: 66, y: 69, village: '和谐村'}
          ],
          color: '#E5CE66',
          name: '平衡型村庄'
        },
        4: { // 待提升村庄
          data: [
            {x: 55, y: 58, village: '梅花村'},
            {x: 52, y: 60, village: '桃花村'},
            {x: 58, y: 55, village: '杏花村'},
            {x: 54, y: 62, village: '樱花村'},
            {x: 56, y: 57, village: '荷花村'}
          ],
          color: '#E74C3C',
          name: '待提升村庄'
        },
        5: { // 特色型村庄
          data: [
            {x: 45, y: 85, village: '竹林村'},
            {x: 48, y: 88, village: '松林村'},
            {x: 42, y: 82, village: '柏林村'},
            {x: 50, y: 90, village: '梧桐村'},
            {x: 46, y: 86, village: '槐树村'}
          ],
          color: '#8199C7',
          name: '特色型村庄'
        }
      };

      // 创建聚类散点图
      const scatterCtx = document.getElementById('cluster-scatter-chart').getContext('2d');
      const scatterChart = new Chart(scatterCtx, {
        type: 'scatter',
        data: {
          datasets: Object.keys(clusterData).map(key => ({
            label: clusterData[key].name,
            data: clusterData[key].data,
            backgroundColor: clusterData[key].color,
            borderColor: clusterData[key].color,
            pointRadius: 8,
            pointHoverRadius: 10
          }))
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                title: function(context) {
                  return context[0].raw.village;
                },
                label: function(context) {
                  return `产业发展指数: ${context.raw.x}, 生活水平指数: ${context.raw.y}`;
                }
              }
            }
          },
          scales: {
            x: {
              title: {
                display: true,
                text: '产业发展指数',
                color: '#64748B'
              },
              min: 30,
              max: 100,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B'
              }
            },
            y: {
              title: {
                display: true,
                text: '生活水平指数',
                color: '#64748B'
              },
              min: 30,
              max: 100,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B'
              }
            }
          },
          onClick: function(event, elements) {
            if (elements.length > 0) {
              const datasetIndex = elements[0].datasetIndex;
              const clusterId = datasetIndex + 1;
              selectCluster(clusterId);
            }
          }
        }
      });

      // 创建雷达图
      const radarCtx = document.getElementById('cluster-radar-chart').getContext('2d');
      let radarChart = new Chart(radarCtx, {
        type: 'radar',
        data: {
          labels: ['产业发展', '生活水平', '人口活力', '基础设施', '环境质量', '创新能力'],
          datasets: [{
            label: '发达型村庄',
            data: [90, 88, 85, 92, 78, 82],
            borderColor: '#31969A',
            backgroundColor: 'rgba(49, 150, 154, 0.2)',
            pointBackgroundColor: '#31969A',
            pointBorderColor: '#FFFFFF',
            pointBorderWidth: 2,
            pointRadius: 5
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            r: {
              beginAtZero: true,
              max: 100,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              angleLines: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              pointLabels: {
                color: '#64748B',
                font: {
                  size: 12
                }
              },
              ticks: {
                display: false
              }
            }
          }
        }
      });

      // 聚类选择功能
      function selectCluster(clusterId) {
        // 更新图例状态
        document.querySelectorAll('.cluster-legend').forEach(legend => {
          legend.classList.remove('active');
        });
        document.querySelector(`[data-cluster="${clusterId}"]`).classList.add('active');

        // 更新选中的聚类名称
        const clusterName = clusterData[clusterId].name;
        const badge = document.getElementById('selected-cluster-name');
        badge.textContent = clusterName;
        badge.className = `cluster-badge badge-cluster-${clusterId}`;

        // 更新雷达图数据
        const radarData = getRadarDataForCluster(clusterId);
        radarChart.data.datasets[0].label = clusterName;
        radarChart.data.datasets[0].data = radarData.values;
        radarChart.data.datasets[0].borderColor = clusterData[clusterId].color;
        radarChart.data.datasets[0].backgroundColor = clusterData[clusterId].color + '33';
        radarChart.data.datasets[0].pointBackgroundColor = clusterData[clusterId].color;
        radarChart.update();

        // 更新特征描述
        updateClusterFeatures(clusterId);
      }

      // 获取聚类雷达图数据
      function getRadarDataForCluster(clusterId) {
        const radarDataMap = {
          1: { values: [90, 88, 85, 92, 78, 82] }, // 发达型
          2: { values: [75, 72, 78, 68, 82, 70] }, // 成长型
          3: { values: [65, 68, 62, 70, 75, 58] }, // 平衡型
          4: { values: [55, 58, 52, 48, 65, 45] }, // 待提升
          5: { values: [48, 85, 42, 55, 88, 92] }  // 特色型
        };
        return radarDataMap[clusterId];
      }

      // 更新聚类特征描述
      function updateClusterFeatures(clusterId) {
        const features = {
          1: { // 发达型村庄
            energy: ['产业用电占比高达68.5%，显著高于其他类型村庄', '居民用电均值为2,850kWh/户，处于较高水平', '用电增长率稳定在15-20%区间，发展势头良好'],
            industry: ['第二、三产业发达，占总产值的85%以上', '制造业和服务业为主导产业', '产业多样化程度高，抗风险能力强'],
            development: ['人均可支配收入达到35,200元，位居前列', '就业率高达92%，人口活力充沛', '基础设施完善，生活质量优良'],
            suggestion: ['继续发挥产业优势，推动高质量发展', '加强创新驱动，提升产业技术含量', '发挥示范带动作用，助力周边村庄发展']
          },
          2: { // 成长型村庄
            energy: ['产业用电占比52.3%，呈稳步上升趋势', '居民用电均值为2,150kWh/户，增长潜力较大', '用电结构逐步优化，清洁能源使用增加'],
            industry: ['第一产业仍占主导，但第二、三产业快速发展', '农产品加工业和乡村旅游业兴起', '产业转型升级步伐加快'],
            development: ['人均可支配收入为28,600元，增长较快', '就业率达到85%，青壮年回流明显', '基础设施不断完善，发展条件改善'],
            suggestion: ['加快产业结构调整，培育新兴产业', '完善基础设施建设，提升发展承载力', '加强人才培养，增强发展内生动力']
          },
          3: { // 平衡型村庄
            energy: ['产业用电占比45.7%，各行业用电相对均衡', '居民用电均值为1,920kWh/户，处于中等水平', '用电增长平稳，结构较为合理'],
            industry: ['三次产业结构相对均衡，发展较为稳定', '传统农业与现代农业并存', '小规模工业和服务业稳步发展'],
            development: ['人均可支配收入为22,800元，增长稳定', '就业率为78%，人口结构相对稳定', '基础设施基本完善，生活条件良好'],
            suggestion: ['优化产业结构，提升发展质量', '加强特色产业培育，形成竞争优势', '完善公共服务，提升民生保障水平']
          },
          4: { // 待提升村庄
            energy: ['产业用电占比38.2%，主要以农业用电为主', '居民用电均值为1,650kWh/户，水平较低', '用电增长缓慢，发展动力不足'],
            industry: ['以传统农业为主，产业结构单一', '工业基础薄弱，服务业发展滞后', '缺乏主导产业和特色产业'],
            development: ['人均可支配收入为18,500元，增长缓慢', '就业率为65%，人口外流严重', '基础设施相对落后，发展条件有限'],
            suggestion: ['加大扶持力度，改善发展条件', '培育特色产业，增强造血功能', '完善基础设施，提升发展环境']
          },
          5: { // 特色型村庄
            energy: ['产业用电占比35.8%，但居民用电水平较高', '居民用电均值为3,200kWh/户，生活品质优良', '清洁能源使用率高，绿色发展特色明显'],
            industry: ['以生态农业和乡村旅游为特色', '文化创意产业和手工业发达', '绿色发展理念深入人心'],
            development: ['人均可支配收入为31,500元，主要来源于特色产业', '就业率为88%，本地就业机会较多', '生态环境优美，宜居宜业'],
            suggestion: ['深化特色产业发展，打造品牌优势', '加强生态保护，实现可持续发展', '完善旅游配套设施，提升服务水平']
          }
        };

        const clusterFeatures = features[clusterId];
        const featuresContainer = document.getElementById('cluster-features');

        featuresContainer.innerHTML = `
          <div class="border-l-4 border-primary pl-4">
            <h4 class="font-semibold text-text-primary mb-2">用能特征</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              ${clusterFeatures.energy.map(item => `<li>• ${item}</li>`).join('')}
            </ul>
          </div>
          <div class="border-l-4 border-success pl-4">
            <h4 class="font-semibold text-text-primary mb-2">产业结构</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              ${clusterFeatures.industry.map(item => `<li>• ${item}</li>`).join('')}
            </ul>
          </div>
          <div class="border-l-4 border-accent pl-4">
            <h4 class="font-semibold text-text-primary mb-2">发展水平</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              ${clusterFeatures.development.map(item => `<li>• ${item}</li>`).join('')}
            </ul>
          </div>
          <div class="border-l-4 border-warning pl-4">
            <h4 class="font-semibold text-text-primary mb-2">发展建议</h4>
            <ul class="text-sm text-text-secondary space-y-1">
              ${clusterFeatures.suggestion.map(item => `<li>• ${item}</li>`).join('')}
            </ul>
          </div>
        `;
      }

      // 聚类图例点击事件
      document.querySelectorAll('.cluster-legend').forEach(legend => {
        legend.addEventListener('click', function() {
          const clusterId = parseInt(this.dataset.cluster);
          selectCluster(clusterId);
        });
      });

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const searchParams = {
          villageName: document.getElementById('village-name').value,
          city: document.getElementById('city-select').value,
          district: document.getElementById('district-select').value,
          cluster: document.getElementById('cluster-select').value,
          scoreMin: document.getElementById('score-min').value,
          scoreMax: document.getElementById('score-max').value,
          riskLevel: document.getElementById('risk-level').value
        };

        console.log('查询条件:', searchParams);
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置按钮事件
      document.getElementById('btn-reset').addEventListener('click', function () {
        document.getElementById('village-name').value = '';
        document.getElementById('city-select').value = '';
        document.getElementById('district-select').value = '';
        document.getElementById('cluster-select').value = '';
        document.getElementById('score-min').value = '';
        document.getElementById('score-max').value = '';
        document.getElementById('risk-level').value = '';
      });

      // 导出数据按钮
      document.getElementById('btn-export').addEventListener('click', function () {
        alert('导出聚类分析数据\n\n将生成包含所有村庄聚类信息的Excel文件');
      });

      // 深度分析按钮
      document.getElementById('btn-analysis').addEventListener('click', function () {
        alert('深度分析\n\n这里将打开高级分析工具，提供更详细的聚类分析功能');
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-eye')) {
          const row = e.target.closest('tr');
          const villageName = row.cells[1].querySelector('.font-medium').textContent;
          alert(`查看村庄详情：${villageName}\n\n这里将显示村庄的详细信息和历史数据`);
        }

        if (e.target.closest('.fa-user')) {
          const row = e.target.closest('tr');
          const villageName = row.cells[1].querySelector('.font-medium').textContent;
          alert(`村庄画像分析：${villageName}\n\n这里将显示该村庄的详细画像分析报告`);
        }
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });

      // 初始化默认选中第一个聚类
      selectCluster(1);
    });
  </script>
</body>

</html>
