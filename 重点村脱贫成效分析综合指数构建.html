<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 重点村脱贫成效分析综合指数构建 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .indicator-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
      }
      .indicator-card:hover {
        border-left-color: #31969A;
        background-color: rgba(49, 150, 154, 0.02);
      }
      .indicator-card.selected {
        border-left-color: #31969A;
        background-color: rgba(49, 150, 154, 0.05);
      }
      .weight-slider {
        -webkit-appearance: none;
        appearance: none;
        height: 6px;
        border-radius: 3px;
        background: #E2E8F0;
        outline: none;
      }
      .weight-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #31969A;
        cursor: pointer;
        border: 2px solid #FFFFFF;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .weight-slider::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #31969A;
        cursor: pointer;
        border: 2px solid #FFFFFF;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .indicator-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-selected { background-color: rgba(49, 150, 154, 0.1); color: #31969A; }
      .badge-unselected { background-color: rgba(148, 163, 184, 0.1); color: #94A3B8; }
      .badge-industry { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-population { background-color: rgba(129, 153, 199, 0.1); color: #8199C7; }
      .badge-life { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-calculator text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 重点村脱贫成效分析综合指数构建</h1>
          <p class="text-sm text-text-secondary">定义综合评分规则，构建可量化的脱贫成效分析综合指数计算模型</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">

    <!-- 综合指数配置面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">综合指数配置</h2>
      
      <!-- 方案基础信息 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">方案基础信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">方案名称 *</label>
            <input type="text" id="scheme-name" placeholder="请输入方案名称" value="标准权重方案v1.0" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">聚合算法 *</label>
            <div class="relative">
              <select id="aggregation-algorithm" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="weighted_sum">加权求和</option>
                <option value="weighted_average">加权平均</option>
                <option value="geometric_mean">几何平均</option>
                <option value="harmonic_mean">调和平均</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>

          <div class="flex flex-col">
            <label class="text-text-secondary mb-2">适用场景</label>
            <div class="relative">
              <select id="application-scenario" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
                <option value="">请选择适用场景</option>
                <option value="quarterly">季度评估</option>
                <option value="annual">年度评估</option>
                <option value="project">项目评估</option>
                <option value="comparison">对比分析</option>
              </select>
              <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                <i class="fa fa-chevron-down text-text-secondary"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 方案描述 -->
      <div class="mb-6">
        <h3 class="text-lg font-medium text-text-primary mb-4 border-b border-border pb-2">方案描述</h3>
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">方案说明</label>
          <textarea id="scheme-description" rows="3" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors resize-none" placeholder="请描述该权重方案的设计思路、适用条件和预期效果">基于"产业发展为主导、生活水平为核心、人口活力为支撑"的评价理念，构建的综合指数计算模型。适用于重点村脱贫成效的全面评估和横向对比。</textarea>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-load-scheme" class="px-4 py-3 bg-secondary text-white rounded-lg border border-secondary hover:bg-secondary/90 transition-colors">
            <i class="fa fa-folder-open mr-1"></i> 加载方案
          </button>
          <button id="btn-reset-scheme" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置配置
          </button>
          <button id="btn-test-model" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-flask mr-1"></i> 模型测试
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-save-scheme" class="px-4 py-3 bg-warning text-white rounded-lg border border-warning hover:bg-warning/90 transition-colors">
            <i class="fa fa-save mr-1"></i> 保存方案
          </button>
          <button id="btn-generate-model" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
            <i class="fa fa-cog mr-1"></i> 生成模型
          </button>
        </div>
      </div>
    </div>

    <!-- 指标选择与权重配置 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- 可选指标库 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">可选指标库</h3>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-text-secondary">28个指标</span>
            <button class="text-primary hover:text-primary/80 transition-colors">
              <i class="fa fa-filter"></i>
            </button>
          </div>
        </div>

        <!-- 维度筛选 -->
        <div class="mb-4">
          <div class="flex flex-wrap gap-2">
            <button class="px-3 py-1 bg-primary text-white rounded-full text-sm border border-primary" data-dimension="all">
              全部
            </button>
            <button class="px-3 py-1 bg-light text-text-secondary rounded-full text-sm border border-border hover:bg-light/70" data-dimension="industry">
              产业发展
            </button>
            <button class="px-3 py-1 bg-light text-text-secondary rounded-full text-sm border border-border hover:bg-light/70" data-dimension="life">
              生活水平
            </button>
            <button class="px-3 py-1 bg-light text-text-secondary rounded-full text-sm border border-border hover:bg-light/70" data-dimension="population">
              人口活力
            </button>
          </div>
        </div>

        <!-- 指标列表 -->
        <div class="space-y-3 max-h-96 overflow-y-auto scrollbar-thin">
          <div class="indicator-card bg-light/30 border border-border rounded-lg p-4 cursor-pointer" data-indicator="IND_001">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <input type="checkbox" class="rounded border-border">
                <span class="font-medium text-text-primary">人均可支配收入</span>
              </div>
              <span class="indicator-badge badge-life">生活水平</span>
            </div>
            <div class="text-sm text-text-secondary">反映村民收入水平的核心指标</div>
            <div class="text-xs text-text-tertiary mt-1">单位：元 | 数据来源：政府统计</div>
          </div>

          <div class="indicator-card bg-light/30 border border-border rounded-lg p-4 cursor-pointer selected" data-indicator="IND_002">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <input type="checkbox" class="rounded border-border" checked>
                <span class="font-medium text-text-primary">产业用电量增长率</span>
              </div>
              <span class="indicator-badge badge-industry">产业发展</span>
            </div>
            <div class="text-sm text-text-secondary">基于电力数据分析产业发展活力</div>
            <div class="text-xs text-text-tertiary mt-1">单位：% | 数据来源：电力数据</div>
          </div>

          <div class="indicator-card bg-light/30 border border-border rounded-lg p-4 cursor-pointer" data-indicator="IND_003">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <input type="checkbox" class="rounded border-border">
                <span class="font-medium text-text-primary">就业率</span>
              </div>
              <span class="indicator-badge badge-population">人口活力</span>
            </div>
            <div class="text-sm text-text-secondary">劳动力就业状况评估指标</div>
            <div class="text-xs text-text-tertiary mt-1">单位：% | 数据来源：实地调研</div>
          </div>
        </div>
      </div>

      <!-- 已选指标配置 -->
      <div class="lg:col-span-2 bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">已选指标权重配置</h3>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-text-secondary">12个已选</span>
            <button id="btn-clear-all" class="text-danger hover:text-danger/80 transition-colors">
              <i class="fa fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- 权重配置列表 -->
        <div class="space-y-4 max-h-96 overflow-y-auto scrollbar-thin">
          <div class="bg-light/50 border border-border rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <span class="font-medium text-text-primary">产业用电量增长率</span>
                <span class="indicator-badge badge-industry">产业发展</span>
              </div>
              <button class="text-danger hover:text-danger/80 transition-colors">
                <i class="fa fa-times"></i>
              </button>
            </div>
            <div class="flex items-center space-x-4">
              <div class="flex-1">
                <input type="range" class="w-full weight-slider" min="0" max="100" value="20" data-indicator="IND_002">
              </div>
              <div class="w-20 text-center">
                <input type="number" class="w-full bg-lighter border border-border rounded px-2 py-1 text-center text-sm" value="20" min="0" max="100">
                <div class="text-xs text-text-secondary">%</div>
              </div>
            </div>
            <div class="text-sm text-text-secondary mt-2">基于电力数据分析产业发展活力，反映产业振兴成效</div>
          </div>

          <div class="bg-light/50 border border-border rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <span class="font-medium text-text-primary">人均可支配收入</span>
                <span class="indicator-badge badge-life">生活水平</span>
              </div>
              <button class="text-danger hover:text-danger/80 transition-colors">
                <i class="fa fa-times"></i>
              </button>
            </div>
            <div class="flex items-center space-x-4">
              <div class="flex-1">
                <input type="range" class="w-full weight-slider" min="0" max="100" value="18" data-indicator="IND_001">
              </div>
              <div class="w-20 text-center">
                <input type="number" class="w-full bg-lighter border border-border rounded px-2 py-1 text-center text-sm" value="18" min="0" max="100">
                <div class="text-xs text-text-secondary">%</div>
              </div>
            </div>
            <div class="text-sm text-text-secondary mt-2">反映村民收入水平的核心指标，直接体现脱贫成效</div>
          </div>

          <div class="bg-light/50 border border-border rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <span class="font-medium text-text-primary">产业总产值</span>
                <span class="indicator-badge badge-industry">产业发展</span>
              </div>
              <button class="text-danger hover:text-danger/80 transition-colors">
                <i class="fa fa-times"></i>
              </button>
            </div>
            <div class="flex items-center space-x-4">
              <div class="flex-1">
                <input type="range" class="w-full weight-slider" min="0" max="100" value="15" data-indicator="IND_004">
              </div>
              <div class="w-20 text-center">
                <input type="number" class="w-full bg-lighter border border-border rounded px-2 py-1 text-center text-sm" value="15" min="0" max="100">
                <div class="text-xs text-text-secondary">%</div>
              </div>
            </div>
            <div class="text-sm text-text-secondary mt-2">村庄产业发展规模核心指标，衡量经济总量</div>
          </div>

          <div class="bg-light/50 border border-border rounded-lg p-4">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-3">
                <span class="font-medium text-text-primary">就业率</span>
                <span class="indicator-badge badge-population">人口活力</span>
              </div>
              <button class="text-danger hover:text-danger/80 transition-colors">
                <i class="fa fa-times"></i>
              </button>
            </div>
            <div class="flex items-center space-x-4">
              <div class="flex-1">
                <input type="range" class="w-full weight-slider" min="0" max="100" value="12" data-indicator="IND_003">
              </div>
              <div class="w-20 text-center">
                <input type="number" class="w-full bg-lighter border border-border rounded px-2 py-1 text-center text-sm" value="12" min="0" max="100">
                <div class="text-xs text-text-secondary">%</div>
              </div>
            </div>
            <div class="text-sm text-text-secondary mt-2">劳动力就业状况评估指标，反映人口活力</div>
          </div>
        </div>

        <!-- 权重总和显示 -->
        <div class="mt-6 pt-4 border-t border-border">
          <div class="flex justify-between items-center">
            <span class="text-lg font-medium text-text-primary">权重总和：</span>
            <div class="flex items-center space-x-2">
              <span id="total-weight" class="text-2xl font-bold text-primary">65%</span>
              <div class="flex items-center space-x-1">
                <div class="w-4 h-4 rounded-full bg-warning"></div>
                <span class="text-sm text-text-secondary">需要调整至100%</span>
              </div>
            </div>
          </div>
          <div class="mt-3">
            <div class="w-full bg-light rounded-full h-3">
              <div class="bg-gradient-to-r from-primary to-warning h-3 rounded-full transition-all duration-300" style="width: 65%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权重方案管理 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">权重方案管理</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出方案
          </button>
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-upload mr-1"></i> 导入方案
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 5%;">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 20%;">方案名称</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">聚合算法</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">指标数量</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">适用场景</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 25%;">方案描述</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 8%;">状态</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 8%;">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">标准权重方案v1.0</div>
                <div class="text-sm text-text-secondary mt-1">创建时间：2025-08-04</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">加权求和</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">12个</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">年度评估</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">基于产业发展为主导的综合评价模型，适用于重点村年度脱贫成效评估</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="px-2 py-1 bg-success/10 text-success rounded-full text-sm">当前使用</span>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="加载">
                  <i class="fa fa-play"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">生活优先方案v1.0</div>
                <div class="text-sm text-text-secondary mt-1">创建时间：2025-07-28</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">加权平均</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">10个</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">季度评估</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">以生活水平提升为核心的评价模型，重点关注民生改善成效</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="px-2 py-1 bg-warning/10 text-warning rounded-full text-sm">备用方案</span>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="加载">
                  <i class="fa fa-play"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">均衡发展方案v1.0</div>
                <div class="text-sm text-text-secondary mt-1">创建时间：2025-07-15</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">几何平均</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">15个</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">对比分析</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm">各维度均衡发展的综合评价模型，适用于村庄间横向对比分析</div>
              </td>
              <td class="px-4 py-4 border-r border-border">
                <span class="px-2 py-1 bg-text-tertiary/10 text-text-tertiary rounded-full text-sm">历史版本</span>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="加载">
                  <i class="fa fa-play"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 3 条，每页显示 10 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 权重滑块和数字输入框同步
      const weightSliders = document.querySelectorAll('.weight-slider');
      const numberInputs = document.querySelectorAll('input[type="number"]');

      weightSliders.forEach((slider, index) => {
        const numberInput = numberInputs[index];

        slider.addEventListener('input', function() {
          numberInput.value = this.value;
          updateTotalWeight();
        });

        numberInput.addEventListener('input', function() {
          slider.value = this.value;
          updateTotalWeight();
        });
      });

      // 更新权重总和
      function updateTotalWeight() {
        let total = 0;
        numberInputs.forEach(input => {
          total += parseInt(input.value) || 0;
        });

        const totalWeightElement = document.getElementById('total-weight');
        totalWeightElement.textContent = total + '%';

        // 更新进度条
        const progressBar = document.querySelector('.bg-gradient-to-r');
        progressBar.style.width = Math.min(total, 100) + '%';

        // 更新颜色和状态
        if (total === 100) {
          totalWeightElement.className = 'text-2xl font-bold text-success';
          progressBar.className = 'bg-success h-3 rounded-full transition-all duration-300';
        } else if (total > 100) {
          totalWeightElement.className = 'text-2xl font-bold text-danger';
          progressBar.className = 'bg-danger h-3 rounded-full transition-all duration-300';
        } else {
          totalWeightElement.className = 'text-2xl font-bold text-primary';
          progressBar.className = 'bg-gradient-to-r from-primary to-warning h-3 rounded-full transition-all duration-300';
        }
      }

      // 指标选择事件
      document.addEventListener('change', function(e) {
        if (e.target.type === 'checkbox' && e.target.closest('.indicator-card')) {
          const card = e.target.closest('.indicator-card');
          if (e.target.checked) {
            card.classList.add('selected');
          } else {
            card.classList.remove('selected');
          }
        }
      });

      // 维度筛选按钮
      document.querySelectorAll('[data-dimension]').forEach(button => {
        button.addEventListener('click', function() {
          // 更新按钮状态
          document.querySelectorAll('[data-dimension]').forEach(btn => {
            btn.className = 'px-3 py-1 bg-light text-text-secondary rounded-full text-sm border border-border hover:bg-light/70';
          });
          this.className = 'px-3 py-1 bg-primary text-white rounded-full text-sm border border-primary';

          // 这里可以添加筛选逻辑
          console.log('筛选维度:', this.dataset.dimension);
        });
      });

      // 加载方案按钮
      document.getElementById('btn-load-scheme').addEventListener('click', function() {
        alert('加载权重方案\n\n这里将显示已保存的权重方案列表供选择');
      });

      // 重置配置按钮
      document.getElementById('btn-reset-scheme').addEventListener('click', function() {
        if (confirm('确定要重置当前配置吗？所有未保存的更改将丢失。')) {
          // 重置所有权重为0
          weightSliders.forEach(slider => slider.value = 0);
          numberInputs.forEach(input => input.value = 0);
          updateTotalWeight();
          alert('配置已重置');
        }
      });

      // 模型测试按钮
      document.getElementById('btn-test-model').addEventListener('click', function() {
        const total = Array.from(numberInputs).reduce((sum, input) => sum + (parseInt(input.value) || 0), 0);
        if (total !== 100) {
          alert('权重总和必须为100%才能进行模型测试');
          return;
        }
        alert('模型测试\n\n这里将使用当前配置对样本数据进行测试计算');
      });

      // 保存方案按钮
      document.getElementById('btn-save-scheme').addEventListener('click', function() {
        const schemeName = document.getElementById('scheme-name').value;
        const algorithm = document.getElementById('aggregation-algorithm').value;

        if (!schemeName) {
          alert('请输入方案名称');
          return;
        }

        const total = Array.from(numberInputs).reduce((sum, input) => sum + (parseInt(input.value) || 0), 0);
        if (total !== 100) {
          alert('权重总和必须为100%才能保存方案');
          return;
        }

        console.log('保存方案:', {
          name: schemeName,
          algorithm: algorithm,
          weights: Array.from(numberInputs).map(input => parseInt(input.value))
        });

        alert('权重方案保存成功！');
      });

      // 生成模型按钮
      document.getElementById('btn-generate-model').addEventListener('click', function() {
        const total = Array.from(numberInputs).reduce((sum, input) => sum + (parseInt(input.value) || 0), 0);
        if (total !== 100) {
          alert('权重总和必须为100%才能生成模型');
          return;
        }

        alert('综合指数计算模型生成成功！\n\n模型已可用于重点村脱贫成效分析');
      });

      // 清空所有已选指标
      document.getElementById('btn-clear-all').addEventListener('click', function() {
        if (confirm('确定要清空所有已选指标吗？')) {
          // 这里可以添加清空逻辑
          alert('已清空所有已选指标');
        }
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });

      // 初始化权重总和
      updateTotalWeight();
    });
  </script>
</body>

</html>
