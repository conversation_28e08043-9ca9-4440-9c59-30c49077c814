<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>分布式光伏损失电量分析系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 100%;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-sun-o text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年7月17日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">管理单位</label>
          <div class="relative">
            <select id="management-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部单位</option>
              <option value="unit1">第一管理单位</option>
              <option value="unit2">第二管理单位</option>
              <option value="unit3">第三管理单位</option>
              <option value="unit4">第四管理单位</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">统计日期</label>
          <div class="flex space-x-3">
            <input type="date" id="main-date" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            <button id="btn-search" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
              <i class="fa fa-search mr-1"></i> 查询
            </button>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">对比曲线</label>
          <div class="flex space-x-3">
            <button id="btn-add-comparison" class="flex-1 px-4 py-3 bg-secondary/20 text-primary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors flex items-center justify-center">
              <i class="fa fa-plus mr-1"></i> 添加日期
            </button>
            <button id="btn-export" class="px-4 py-3 bg-primary/20 text-primary rounded-lg border border-primary/30 hover:bg-primary/30 transition-colors">
              <i class="fa fa-download mr-1"></i> 导出
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-semibold text-text-primary mb-4 md:mb-0">分布式光伏损失电量对比曲线 (按小时统计)</h2>
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center">
            <div class="h-3 w-3 rounded-full bg-primary mr-2"></div>
            <span class="text-text-secondary">今日 (2025-07-17)</span>
          </div>
          <div class="flex items-center">
            <div class="h-3 w-3 rounded-full bg-secondary mr-2"></div>
            <span class="text-text-secondary">昨日 (2025-07-16)</span>
          </div>
          <div class="flex items-center">
            <div class="h-3 w-3 rounded-full bg-accent mr-2"></div>
            <span class="text-text-secondary">上月 (2025-06-17)</span>
          </div>
        </div>
      </div>
      <div class="h-[500px] chart-container">
        <canvas id="power-output-chart"></canvas>
      </div>
    </div>

    <!-- 对比日期列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">已添加对比日期</h3>
        <span class="text-text-secondary">点击可移除对比曲线</span>
      </div>
      <div id="comparison-dates" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div class="bg-light border border-border rounded-lg p-3 flex justify-between items-center hover:bg-light/70 transition-colors">
          <span class="text-text-primary">昨日 (2025-07-16)</span>
          <button class="text-text-secondary hover:text-danger transition-colors" data-date="2025-07-16">
            <i class="fa fa-times"></i>
          </button>
        </div>
        <div class="bg-light border border-border rounded-lg p-3 flex justify-between items-center hover:bg-light/70 transition-colors">
          <span class="text-text-primary">上月 (2025-06-17)</span>
          <button class="text-text-secondary hover:text-danger transition-colors" data-date="2025-06-17">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 发电数据统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">今日总损失电量</p>
            <h3 class="text-3xl font-bold mt-2">18.2 <span class="text-lg font-normal text-text-secondary">kWh</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-bolt text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 12.5%
          </span>
          <span class="text-text-secondary ml-2">较昨日</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">峰值损失功率</p>
            <h3 class="text-3xl font-bold mt-2">3.8 <span class="text-lg font-normal text-text-secondary">kW</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-line-chart text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 3.2%
          </span>
          <span class="text-text-secondary ml-2">较昨日</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">平均损失率</p>
            <h3 class="text-3xl font-bold mt-2">8.7 <span class="text-lg font-normal text-text-secondary">%</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
            <i class="fa fa-cogs text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-danger flex items-center">
            <i class="fa fa-arrow-down mr-1"></i> 1.8%
          </span>
          <span class="text-text-secondary ml-2">较昨日</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">预测损失电量</p>
            <h3 class="text-3xl font-bold mt-2">20.3 <span class="text-lg font-normal text-text-secondary">kWh</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-lightbulb-o text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 9.7%
          </span>
          <span class="text-text-secondary ml-2">较昨日</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <!-- 添加对比日期模态框 -->
  <div id="add-comparison-modal" class="fixed inset-0 bg-black/10 flex items-center justify-center z-50 hidden">
    <div class="bg-lighter border border-border rounded-xl p-8 w-full max-w-md transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-semibold text-text-primary">添加对比日期</h3>
        <button id="close-modal" class="text-text-secondary hover:text-primary transition-colors">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>
      <div class="mb-6">
        <label class="text-text-secondary block mb-2">选择日期</label>
        <input type="date" id="comparison-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
      </div>
      <div class="flex justify-end space-x-4">
        <button id="cancel-add-comparison" class="px-6 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
          取消
        </button>
        <button id="confirm-add-comparison" class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
          添加
        </button>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      document.getElementById('main-date').value = formattedDate;

      // 图表数据和配置
      const chartData = {
        labels: Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`),
        datasets: [
          {
            label: '今日 (2025-07-17)',
            data: [0, 0, 0, 0, 0, 0.2, 2.8, 5.6, 7.8, 8.2, 7.5, 8.0, 9.5, 10.2, 16.8, 15.2, 13.8, 11.5, 9.2, 6.5, 3.2, 1.0, 0, 0],
            borderColor: '#31969A',
            backgroundColor: 'rgba(49, 150, 154, 0.05)',
            fill: true,
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 3,
            pointHoverRadius: 5,
            pointBackgroundColor: '#FFFFFF',
            pointBorderColor: '#31969A',
            pointBorderWidth: 2
          },
          {
            label: '昨日 (2025-07-16)',
            data: [0, 0, 0, 0, 0, 0.1, 2.5, 5.2, 7.0, 7.8, 7.2, 7.5, 8.8, 9.5, 15.8, 14.5, 13.2, 10.8, 8.5, 6.2, 3.0, 0.8, 0, 0],
            borderColor: '#80CCE3',
            backgroundColor: 'rgba(128, 204, 227, 0.05)',
            fill: true,
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 3,
            pointHoverRadius: 5,
            pointBackgroundColor: '#FFFFFF',
            pointBorderColor: '#80CCE3',
            pointBorderWidth: 2
          },
          {
            label: '上月 (2025-06-17)',
            data: [0, 0, 0, 0, 0, 0.3, 3.2, 6.5, 8.5, 9.2, 8.8, 9.5, 10.8, 11.5, 14.8, 14.2, 12.8, 10.5, 8.2, 5.8, 2.5, 0.5, 0, 0],
            borderColor: '#8199C7',
            backgroundColor: 'rgba(129, 153, 199, 0.05)',
            fill: true,
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 3,
            pointHoverRadius: 5,
            pointBackgroundColor: '#FFFFFF',
            pointBorderColor: '#8199C7',
            pointBorderWidth: 2
          }
        ]
      };

      // 创建图表
      const ctx = document.getElementById('power-output-chart').getContext('2d');
      const powerOutputChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              padding: 16,
              boxPadding: 8,
              usePointStyle: true,
              callbacks: {
                label: function (context) {
                  return `${context.dataset.label}: ${context.raw} kW`;
                }
              }
            }
          },
          scales: {
            x: {
              grid: {
                color: 'rgba(226, 232, 240, 0.7)',
                width: 1
              },
              ticks: {
                color: '#64748B',
                font: {
                  size: 12
                },
                maxRotation: 0,
                autoSkip: true,
                maxTicksLimit: 12
              }
            },
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)',
                width: 1
              },
              ticks: {
                color: '#64748B',
                font: {
                  size: 12
                },
                callback: function (value) {
                  return value + ' kW';
                }
              }
            }
          },
          animations: {
            tension: {
              duration: 1000,
              easing: 'easeOutQuart'
            }
          }
        }
      });

      // 单位名称切换
      document.getElementById('management-unit').addEventListener('change', function () {
        const selectedUnit = this.value;
        console.log(`选择单位: ${selectedUnit}`);
        updateChartByUnit(selectedUnit);
      });

      // 日期查询
      document.getElementById('btn-search').addEventListener('click', function () {
        const selectedDate = document.getElementById('main-date').value;
        const selectedUnit = document.getElementById('management-unit').value;

        if (selectedDate) {
          console.log(`查询单位: ${selectedUnit}, 日期: ${selectedDate}`);
          updateChartByDate(selectedDate);
        }
      });

      // 添加对比按钮
      document.getElementById('btn-add-comparison').addEventListener('click', function () {
        const modal = document.getElementById('add-comparison-modal');
        const modalContent = document.getElementById('modal-content');

        modal.classList.remove('hidden');
        // 设置默认日期为今天
        document.getElementById('comparison-date').value = formattedDate;

        // 添加动画效果
        setTimeout(() => {
          modalContent.classList.remove('scale-95', 'opacity-0');
          modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
      });

      // 关闭模态框
      document.getElementById('close-modal').addEventListener('click', function () {
        closeModal();
      });

      document.getElementById('cancel-add-comparison').addEventListener('click', function () {
        closeModal();
      });

      // 确认添加对比日期
      document.getElementById('confirm-add-comparison').addEventListener('click', function () {
        const dateInput = document.getElementById('comparison-date');
        const selectedDate = dateInput.value;

        if (selectedDate) {
          // 添加到已选对比日期列表
          addComparisonDate(selectedDate);
          // 关闭模态框
          closeModal();
        }
      });

      // 移除对比日期
      document.querySelectorAll('#comparison-dates button').forEach(button => {
        button.addEventListener('click', function () {
          const date = this.getAttribute('data-date');
          removeComparisonDate(date);
        });
      });

      // 导出按钮
      document.getElementById('btn-export').addEventListener('click', function () {
        console.log('导出数据');
        // 这里可以添加导出数据的逻辑
      });

      // 辅助函数：根据单位更新图表数据
      function updateChartByUnit(unit) {
        // 模拟根据不同单位更新数据
        const unitFactors = {
          'all': 1,
          'unit1': 1.2,
          'unit2': 0.9,
          'unit3': 1.1,
          'unit4': 0.8
        };

        const factor = unitFactors[unit] || 1;

        chartData.datasets.forEach(dataset => {
          dataset.data = dataset.data.map(value => value * factor);
        });

        powerOutputChart.update();
      }

      // 辅助函数：根据日期更新图表数据
      function updateChartByDate(date) {
        // 更新主曲线标签
        const dateObj = new Date(date);
        const formattedDate = `${dateObj.getFullYear()}-${(dateObj.getMonth() + 1).toString().padStart(2, '0')}-${dateObj.getDate().toString().padStart(2, '0')}`;

        chartData.datasets[0].label = `今日 (${formattedDate})`;

        // 模拟根据不同日期更新数据
        const randomVariation = () => 0.8 + (Math.random() * 0.4); // 0.8 to 1.2

        chartData.datasets[0].data = chartData.datasets[0].data.map(value => value * randomVariation());

        powerOutputChart.update();
      }

      // 辅助函数：添加对比日期
      function addComparisonDate(date) {
        // 检查是否已添加该日期
        if (powerOutputChart.data.datasets.some(dataset => dataset.label.includes(date))) {
          alert('该日期已添加为对比');
          return;
        }

        // 生成随机数据
        const randomData = Array.from({ length: 24 }, () => Math.random() * 15);

        // 添加新数据集
        const dateObj = new Date(date);
        const formattedDate = `${dateObj.getFullYear()}-${(dateObj.getMonth() + 1).toString().padStart(2, '0')}-${dateObj.getDate().toString().padStart(2, '0')}`;

        const colors = ['#E5CE66', '#F87272', '#93C5FD', '#EC4899', '#10B981'];
        const usedColors = powerOutputChart.data.datasets.map(dataset => dataset.borderColor);
        const availableColors = colors.filter(color => !usedColors.includes(color));
        const color = availableColors.length > 0 ? availableColors[0] : colors[Math.floor(Math.random() * colors.length)];

        powerOutputChart.data.datasets.push({
          label: `对比 (${formattedDate})`,
          data: randomData,
          borderColor: color,
          backgroundColor: `${color}0D`, // 添加10%的透明度
          fill: true,
          tension: 0.4,
          borderWidth: 3,
          pointRadius: 3,
          pointHoverRadius: 5,
          pointBackgroundColor: '#FFFFFF',
          pointBorderColor: color,
          pointBorderWidth: 2
        });

        // 添加到已选对比日期列表
        const comparisonDatesContainer = document.getElementById('comparison-dates');
        const dateElement = document.createElement('div');
        dateElement.className = 'bg-light border border-border rounded-lg p-3 flex justify-between items-center hover:bg-light/70 transition-colors';
        dateElement.innerHTML = `
          <span class="text-text-primary">对比 (${formattedDate})</span>
          <button class="text-text-secondary hover:text-danger transition-colors" data-date="${formattedDate}">
            <i class="fa fa-times"></i>
          </button>
        `;

        // 添加点击事件
        dateElement.querySelector('button').addEventListener('click', function () {
          const date = this.getAttribute('data-date');
          removeComparisonDate(date);
        });

        comparisonDatesContainer.appendChild(dateElement);

        // 更新图表
        powerOutputChart.update();
      }

      // 辅助函数：移除对比日期
      function removeComparisonDate(date) {
        // 从图表数据中移除
        powerOutputChart.data.datasets = powerOutputChart.data.datasets.filter(dataset => {
          return !dataset.label.includes(date);
        });

        // 从已选对比日期列表中移除
        document.querySelectorAll('#comparison-dates div').forEach(element => {
          if (element.textContent.includes(date)) {
            element.remove();
          }
        });

        // 更新图表
        powerOutputChart.update();
      }

      // 辅助函数：关闭模态框
      function closeModal() {
        const modal = document.getElementById('add-comparison-modal');
        const modalContent = document.getElementById('modal-content');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
          modal.classList.add('hidden');
        }, 300);
      }

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
