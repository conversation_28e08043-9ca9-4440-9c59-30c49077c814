<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 效能分析 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .efficiency-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }
      .badge-excellent { background-color: rgba(16, 185, 129, 0.1); color: #10B981; }
      .badge-good { background-color: rgba(49, 150, 154, 0.1); color: #31969A; }
      .badge-average { background-color: rgba(229, 206, 102, 0.1); color: #E5CE66; }
      .badge-poor { background-color: rgba(231, 76, 60, 0.1); color: #E74C3C; }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-line-chart text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 效能分析</h1>
          <p class="text-sm text-text-secondary">政策策略效能评估与数据可视化分析系统</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="w-full max-w-none mx-auto px-6 py-6">
    <!-- 效能分析概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">分析报告总数</p>
            <h3 class="text-3xl font-bold mt-2">127 <span class="text-lg font-normal text-text-secondary">份</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-file-chart-o text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 23.5%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">监测重点村</p>
            <h3 class="text-3xl font-bold mt-2">45 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-map-marker text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 8.7%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">平均效能得分</p>
            <h3 class="text-3xl font-bold mt-2">87.3 <span class="text-lg font-normal text-text-secondary">分</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-trophy text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 5.2%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">优秀效能村数</p>
            <h3 class="text-3xl font-bold mt-2">32 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-star text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 12.5%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>
    </div>

    <!-- 查询条件面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">效能分析报告查询</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">报告名称</label>
          <input type="text" id="report-name" placeholder="请输入分析报告名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">关联策略</label>
          <div class="relative">
            <select id="related-strategy" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部策略</option>
              <option value="产业振兴评估">产业振兴评估</option>
              <option value="防返贫监测">防返贫监测</option>
              <option value="用能效率提升">用能效率提升</option>
              <option value="电力扶贫成效">电力扶贫成效</option>
              <option value="乡村振兴综合">乡村振兴综合</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">监测对象（重点村）</label>
          <input type="text" id="monitoring-village" placeholder="请输入重点村名称" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">监测周期</label>
          <div class="relative">
            <select id="monitoring-period" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部周期</option>
              <option value="2025年第一季度">2025年第一季度</option>
              <option value="2025年第二季度">2025年第二季度</option>
              <option value="2024年年度">2024年年度</option>
              <option value="2024年第四季度">2024年第四季度</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
        </div>
        <div class="flex space-x-3">
          <button id="btn-dashboard" class="px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
            <i class="fa fa-dashboard mr-1"></i> 数据仪表盘
          </button>
          <button id="btn-new-analysis" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
            <i class="fa fa-plus mr-1"></i> 新建分析
          </button>
        </div>
      </div>
    </div>

    <!-- 多维度数据可视化仪表盘 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 效能趋势分析 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">效能趋势分析</h3>
          <button class="text-primary hover:text-primary/80 transition-colors">
            <i class="fa fa-expand"></i>
          </button>
        </div>
        <div class="chart-container">
          <canvas id="efficiency-trend-chart"></canvas>
        </div>
      </div>

      <!-- 策略效果对比 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">策略效果对比</h3>
          <button class="text-primary hover:text-primary/80 transition-colors">
            <i class="fa fa-expand"></i>
          </button>
        </div>
        <div class="chart-container">
          <canvas id="strategy-comparison-chart"></canvas>
        </div>
      </div>

      <!-- 重点村效能分布 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">重点村效能分布</h3>
          <button class="text-primary hover:text-primary/80 transition-colors">
            <i class="fa fa-expand"></i>
          </button>
        </div>
        <div class="chart-container">
          <canvas id="village-efficiency-chart"></canvas>
        </div>
      </div>

      <!-- 用能数据关联分析 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-xl font-semibold text-text-primary">用能数据关联分析</h3>
          <button class="text-primary hover:text-primary/80 transition-colors">
            <i class="fa fa-expand"></i>
          </button>
        </div>
        <div class="chart-container">
          <canvas id="energy-correlation-chart"></canvas>
        </div>
      </div>
    </div>
    
    <!-- 效能分析报告列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">效能分析报告列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 批量导出
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 22%;">分析报告名称</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">关联策略</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">监测对象</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 12%;">监测周期</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 10%;">效能得分</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border" style="width: 24%;">核心成效结论</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary" style="width: 10%;">操作</th>
            </tr>
          </thead>
          <tbody id="analysis-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">赛罕村产业振兴效能评估报告</div>
                <div class="text-sm text-text-secondary mt-2">基于电力数据的产业发展分析</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="efficiency-badge badge-excellent">产业振兴评估</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">赛罕村</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2025年第二季度</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-xl font-bold text-success">92.5</div>
                <div class="text-sm text-text-secondary">优秀</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm leading-relaxed">产业用电量增长35%，新增就业岗位120个，农民人均收入提升28%，光伏发电项目带动村集体经济收入增加45万元，形成了"光伏+农业+旅游"的产业融合发展模式</div>
              </td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="数据仪表盘">
                  <i class="fa fa-dashboard"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">九原村防返贫监测效能分析</div>
                <div class="text-sm text-text-secondary mt-2">用能数据预警机制效果评估</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="efficiency-badge badge-good">防返贫监测</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">九原村</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2025年第二季度</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-xl font-bold text-primary">88.2</div>
                <div class="text-sm text-text-secondary">良好</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm leading-relaxed">预警准确率达95%，及时发现风险户15户，防返贫成效显著。通过用电量异常监测，成功预警潜在返贫风险，建立了"电力数据+人工核实"的双重保障机制</div>
              </td>
              <td class="px-4 py-4">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="数据仪表盘">
                  <i class="fa fa-dashboard"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-4 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">康巴什村用能效率提升分析</div>
                <div class="text-sm text-text-secondary mt-2">电力数据驱动的节能减排效果</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="efficiency-badge badge-good">用能效率提升</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">康巴什村</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2025年第一季度</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-xl font-bold text-primary">85.7</div>
                <div class="text-sm text-text-secondary">良好</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm leading-relaxed">用电效率提升22%，节约电费支出18万元，绿色发展成效明显。推广智能用电设备，建立用能监测平台，实现了精准用能管理和节能减排目标</div>
              </td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="数据仪表盘">
                  <i class="fa fa-dashboard"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">集宁村电力扶贫成效评估</div>
                <div class="text-sm text-text-secondary mt-2">电力基础设施投资效果分析</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="efficiency-badge badge-average">电力扶贫成效</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">集宁村</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2024年年度</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-xl font-bold text-warning">78.3</div>
                <div class="text-sm text-text-secondary">一般</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm leading-relaxed">电网改造完成率100%，供电可靠性提升至99.8%，惠及农户356户。完成10千伏线路改造15公里，新建配电变压器8台，解决了长期存在的低电压问题</div>
              </td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="数据仪表盘">
                  <i class="fa fa-dashboard"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="font-medium text-base">东胜村乡村振兴综合效能报告</div>
                <div class="text-sm text-text-secondary mt-2">多维度政策策略综合评估</div>
              </td>
              <td class="px-4 py-4 border-r border-border"><span class="efficiency-badge badge-excellent">乡村振兴综合</span></td>
              <td class="px-4 py-4 text-text-primary border-r border-border">东胜村</td>
              <td class="px-4 py-4 text-text-primary border-r border-border">2025年第一季度</td>
              <td class="px-4 py-4 border-r border-border">
                <div class="text-xl font-bold text-success">91.8</div>
                <div class="text-sm text-text-secondary">优秀</div>
              </td>
              <td class="px-4 py-4 text-text-primary border-r border-border">
                <div class="text-sm leading-relaxed">综合发展指数提升40%，产业、人才、文化等五大振兴全面推进。建成现代农业示范园区，引进高层次人才12名，打造特色文化品牌，实现了全面振兴</div>
              </td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="查看详情">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="数据仪表盘">
                  <i class="fa fa-dashboard"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 127 条，每页显示 10 条，第 1 页/共 13 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">2</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">3</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const searchParams = {
          reportName: document.getElementById('report-name').value,
          strategy: document.getElementById('related-strategy').value,
          village: document.getElementById('monitoring-village').value,
          period: document.getElementById('monitoring-period').value
        };

        console.log('查询条件:', searchParams);
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置按钮事件
      document.getElementById('btn-reset').addEventListener('click', function () {
        document.getElementById('report-name').value = '';
        document.getElementById('related-strategy').value = '';
        document.getElementById('monitoring-village').value = '';
        document.getElementById('monitoring-period').value = '';
      });

      // 数据仪表盘按钮
      document.getElementById('btn-dashboard').addEventListener('click', function () {
        alert('打开综合数据仪表盘\n\n这里将显示多维度的效能分析可视化界面');
      });

      // 新建分析按钮
      document.getElementById('btn-new-analysis').addEventListener('click', function () {
        alert('新建效能分析报告\n\n这里将打开分析报告创建向导');
      });

      // 效能趋势分析图表
      const trendCtx = document.getElementById('efficiency-trend-chart').getContext('2d');
      new Chart(trendCtx, {
        type: 'line',
        data: {
          labels: ['2024年Q1', '2024年Q2', '2024年Q3', '2024年Q4', '2025年Q1', '2025年Q2'],
          datasets: [{
            label: '平均效能得分',
            data: [75.2, 78.5, 82.1, 84.7, 86.3, 87.3],
            borderColor: '#31969A',
            backgroundColor: 'rgba(49, 150, 154, 0.1)',
            fill: true,
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 5,
            pointHoverRadius: 7,
            pointBackgroundColor: '#FFFFFF',
            pointBorderColor: '#31969A',
            pointBorderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1
            }
          },
          scales: {
            x: {
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B'
              }
            },
            y: {
              beginAtZero: false,
              min: 70,
              max: 100,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B',
                callback: function(value) {
                  return value + '分';
                }
              }
            }
          }
        }
      });

      // 策略效果对比图表
      const comparisonCtx = document.getElementById('strategy-comparison-chart').getContext('2d');
      new Chart(comparisonCtx, {
        type: 'bar',
        data: {
          labels: ['产业振兴评估', '防返贫监测', '用能效率提升', '电力扶贫成效', '乡村振兴综合'],
          datasets: [{
            label: '效能得分',
            data: [89.5, 85.2, 82.7, 78.9, 91.2],
            backgroundColor: [
              '#10B981',
              '#31969A',
              '#8199C7',
              '#E5CE66',
              '#80CCE3'
            ],
            borderWidth: 0,
            borderRadius: 8
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#64748B',
                maxRotation: 45
              }
            },
            y: {
              beginAtZero: false,
              min: 70,
              max: 100,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B',
                callback: function(value) {
                  return value + '分';
                }
              }
            }
          }
        }
      });

      // 重点村效能分布图表
      const villageCtx = document.getElementById('village-efficiency-chart').getContext('2d');
      new Chart(villageCtx, {
        type: 'doughnut',
        data: {
          labels: ['优秀(90-100分)', '良好(80-89分)', '一般(70-79分)', '待改进(60-69分)'],
          datasets: [{
            data: [32, 8, 4, 1],
            backgroundColor: [
              '#10B981',
              '#31969A',
              '#E5CE66',
              '#E74C3C'
            ],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true
              }
            }
          }
        }
      });

      // 用能数据关联分析图表
      const correlationCtx = document.getElementById('energy-correlation-chart').getContext('2d');
      new Chart(correlationCtx, {
        type: 'scatter',
        data: {
          datasets: [{
            label: '用电量与效能得分关联',
            data: [
              {x: 1200, y: 92.5},
              {x: 980, y: 88.2},
              {x: 850, y: 85.7},
              {x: 720, y: 78.3},
              {x: 1100, y: 91.8},
              {x: 650, y: 75.2},
              {x: 890, y: 83.4},
              {x: 1050, y: 89.1}
            ],
            backgroundColor: '#31969A',
            borderColor: '#31969A',
            pointRadius: 6,
            pointHoverRadius: 8
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            x: {
              title: {
                display: true,
                text: '月均用电量(kWh)',
                color: '#64748B'
              },
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B'
              }
            },
            y: {
              title: {
                display: true,
                text: '效能得分',
                color: '#64748B'
              },
              min: 70,
              max: 100,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)'
              },
              ticks: {
                color: '#64748B',
                callback: function(value) {
                  return value + '分';
                }
              }
            }
          }
        }
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-eye')) {
          const row = e.target.closest('tr');
          const reportName = row.cells[1].querySelector('.font-medium').textContent;
          alert(`查看效能分析详情：${reportName}\n\n这里将显示详细的分析报告内容`);
        }

        if (e.target.closest('.fa-dashboard')) {
          const row = e.target.closest('tr');
          const reportName = row.cells[1].querySelector('.font-medium').textContent;
          alert(`打开数据仪表盘：${reportName}\n\n这里将显示该报告的专属可视化仪表盘`);
        }

        if (e.target.closest('.fa-edit')) {
          const row = e.target.closest('tr');
          const reportName = row.cells[1].querySelector('.font-medium').textContent;
          alert(`编辑分析报告：${reportName}`);
        }

        if (e.target.closest('.fa-trash')) {
          const row = e.target.closest('tr');
          const reportName = row.cells[1].querySelector('.font-medium').textContent;
          if (confirm(`确定要删除分析报告"${reportName}"吗？`)) {
            alert('分析报告已删除');
          }
        }
      });

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
